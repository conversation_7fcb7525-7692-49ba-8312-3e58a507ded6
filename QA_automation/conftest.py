# QA_automation specific conftest.py
# This file overrides any parent conftest.py files to ensure complete isolation

import os
import sys

# Prevent Django from being loaded
os.environ.setdefault('DJANGO_SETTINGS_MODULE', '')

# Remove any Django-related modules that might have been imported
django_modules = [module for module in sys.modules.keys() if module.startswith('django')]
for module in django_modules:
    if module in sys.modules:
        del sys.modules[module]

# Disable pytest-django plugin completely
def pytest_configure(config):
    """Disable Django integration for QA automation tests."""
    # Remove django plugin if it was loaded
    if hasattr(config.pluginmanager, '_name2plugin'):
        if 'django' in config.pluginmanager._name2plugin:
            del config.pluginmanager._name2plugin['django']
    
    # Ensure no Django settings are configured
    os.environ.pop('DJANGO_SETTINGS_MODULE', None)

def pytest_collection_modifyitems(config, items):
    """Ensure no Django-related test collection."""
    pass
