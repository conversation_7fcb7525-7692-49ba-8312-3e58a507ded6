from playwright.sync_api import Page


class ProdSettingsPage:
    """Page Object Model for Settings navigation in Production environment."""

    # Locators - Using specific selectors instead of get_by_role
    SETTINGS_BUTTON = "button:has-text('Settings')"
    PERMISSIONS_TAB = "button:has-text('Permissions')"
    BRANDING_TAB = "button:has-text('Branding')"
    ACCOUNT_TAB = "button:has-text('Account')"
    USER_MANAGEMENT_TAB = "button:has-text('User Management')"
    
    # Alternative locators
    ALT_SETTINGS_BUTTON = "[data-testid='settings-button'], .settings-button"
    ALT_PERMISSIONS_TAB = "[data-testid='permissions-tab'], .permissions-tab"
    ALT_BRANDING_TAB = "[data-testid='branding-tab'], .branding-tab"

    # Timeouts
    DEFAULT_TIMEOUT = 5000
    NAVIGATION_TIMEOUT = 3000

    def __init__(self, page: Page):
        self.page = page

    def navigate_to_settings(self):
        """Navigate to the main Settings page."""
        try:
            # Try to click on Settings button
            settings_button = self.page.locator(self.SETTINGS_BUTTON)
            if settings_button.is_visible(timeout=self.DEFAULT_TIMEOUT):
                settings_button.click()
                print("✓ Clicked on Settings button")
            else:
                # Try alternative selector
                settings_button = self.page.locator(self.ALT_SETTINGS_BUTTON).first
                if settings_button.is_visible():
                    settings_button.click()
                    print("✓ Clicked on Settings button (alternative selector)")
                else:
                    raise Exception("Could not find Settings button")
                    
            self.page.wait_for_timeout(self.NAVIGATION_TIMEOUT)
            
        except Exception as e:
            print(f"❌ Error navigating to settings: {e}")
            self.page.screenshot(path="settings_navigation_error.png")
            raise

    def navigate_to_permissions(self):
        """Navigate to Settings > Permissions."""
        try:
            # First ensure we're in settings
            self.navigate_to_settings()
            
            # Then click on Permissions tab
            permissions_tab = self.page.locator(self.PERMISSIONS_TAB)
            if permissions_tab.is_visible(timeout=self.DEFAULT_TIMEOUT):
                permissions_tab.click()
                print("✓ Clicked on Permissions tab")
            else:
                # Try alternative selector
                permissions_tab = self.page.locator(self.ALT_PERMISSIONS_TAB).first
                if permissions_tab.is_visible():
                    permissions_tab.click()
                    print("✓ Clicked on Permissions tab (alternative selector)")
                else:
                    raise Exception("Could not find Permissions tab")
                    
            self.page.wait_for_timeout(self.NAVIGATION_TIMEOUT)
            
        except Exception as e:
            print(f"❌ Error navigating to permissions: {e}")
            self.page.screenshot(path="permissions_navigation_error.png")
            raise

    def navigate_to_branding(self):
        """Navigate to Settings > Branding."""
        try:
            # First ensure we're in settings
            self.navigate_to_settings()
            
            # Then click on Branding tab
            branding_tab = self.page.locator(self.BRANDING_TAB)
            if branding_tab.is_visible(timeout=self.DEFAULT_TIMEOUT):
                branding_tab.click()
                print("✓ Clicked on Branding tab")
            else:
                # Try alternative selector
                branding_tab = self.page.locator(self.ALT_BRANDING_TAB).first
                if branding_tab.is_visible():
                    branding_tab.click()
                    print("✓ Clicked on Branding tab (alternative selector)")
                else:
                    raise Exception("Could not find Branding tab")
                    
            self.page.wait_for_timeout(self.NAVIGATION_TIMEOUT)
            
        except Exception as e:
            print(f"❌ Error navigating to branding: {e}")
            self.page.screenshot(path="branding_navigation_error.png")
            raise

    def navigate_to_account(self):
        """Navigate to Settings > Account."""
        try:
            # First ensure we're in settings
            self.navigate_to_settings()
            
            # Then click on Account tab
            account_tab = self.page.locator(self.ACCOUNT_TAB)
            if account_tab.is_visible(timeout=self.DEFAULT_TIMEOUT):
                account_tab.click()
                print("✓ Clicked on Account tab")
            else:
                raise Exception("Could not find Account tab")
                    
            self.page.wait_for_timeout(self.NAVIGATION_TIMEOUT)
            
        except Exception as e:
            print(f"❌ Error navigating to account: {e}")
            self.page.screenshot(path="account_navigation_error.png")
            raise

    def navigate_to_user_management(self):
        """Navigate to Settings > User Management."""
        try:
            # First ensure we're in settings
            self.navigate_to_settings()
            
            # Then click on User Management tab
            user_mgmt_tab = self.page.locator(self.USER_MANAGEMENT_TAB)
            if user_mgmt_tab.is_visible(timeout=self.DEFAULT_TIMEOUT):
                user_mgmt_tab.click()
                print("✓ Clicked on User Management tab")
            else:
                raise Exception("Could not find User Management tab")
                    
            self.page.wait_for_timeout(self.NAVIGATION_TIMEOUT)
            
        except Exception as e:
            print(f"❌ Error navigating to user management: {e}")
            self.page.screenshot(path="user_management_navigation_error.png")
            raise

    def verify_settings_page_loaded(self):
        """Verify that the settings page has loaded correctly."""
        try:
            # Check for common settings page elements
            settings_indicators = [
                self.PERMISSIONS_TAB,
                self.ACCOUNT_TAB,
                "h1:has-text('Settings')",
                ".settings-container",
                "[data-testid='settings-page']"
            ]
            
            for indicator in settings_indicators:
                if self.page.locator(indicator).is_visible(timeout=2000):
                    print("✓ Settings page loaded successfully")
                    return True
                    
            print("⚠️ Settings page load verification inconclusive")
            return False
            
        except Exception as e:
            print(f"❌ Error verifying settings page: {e}")
            return False
