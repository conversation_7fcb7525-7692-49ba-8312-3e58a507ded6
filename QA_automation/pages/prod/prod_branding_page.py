from playwright.sync_api import Page
import hashlib
import os


class ProdBrandingPage:
    """Simplified Page Object for Custom Logo functionality."""

    def __init__(self, page: Page):
        self.page = page
        self.original_logo_hash = None  # Store hash of original logo for comparison

    def capture_original_logo_state(self):
        """Capture the current logo state before upload for comparison."""
        try:
            # Look for existing logo images on the page
            logo_selectors = [
                "img[src*='logo']",
                "img[alt*='logo']",
                ".logo img",
                "[data-testid*='logo'] img",
                "img[src*='brand']"
            ]

            logo_info = {
                'found': False,
                'src': None,
                'hash': None,
                'selector_used': None
            }

            for selector in logo_selectors:
                logos = self.page.locator(selector)
                if logos.count() > 0:
                    # Get the src attribute of the first logo found
                    logo_src = logos.first.get_attribute('src')
                    if logo_src:
                        self.original_logo_hash = hashlib.md5(logo_src.encode()).hexdigest()
                        logo_info = {
                            'found': True,
                            'src': logo_src,
                            'hash': self.original_logo_hash,
                            'selector_used': selector
                        }
                        print(f"✓ Original logo state captured: {logo_src[:50]}...")
                        return logo_info

            print("⚠️ No original logo found to compare against")
            return logo_info

        except Exception as e:
            print(f"❌ Error capturing original logo state: {e}")
            return {'found': False, 'error': str(e)}

    def verify_logo_changed(self, original_logo_info):
        """Verify that the logo has actually changed from the original."""
        try:
            # Wait 5 seconds for page to update after upload
            print("⏳ Waiting 5 seconds for logo to update...")
            self.page.wait_for_timeout(5000)

            # Look for logo images again using expanded selectors
            logo_selectors = [
                "img[src*='blob:']",  # Uploaded files often use blob URLs first
                "img[src*='data:']",  # Base64 encoded images
                "img[src*='logo']",
                "img[alt*='logo']",
                ".logo img",
                "[data-testid*='logo'] img",
                "img[src*='brand']",
                "img",  # All images as fallback
                "canvas",  # Sometimes logos are rendered in canvas
                "[style*='background-image']"  # CSS background images
            ]

            current_logo_info = {
                'found': False,
                'src': None,
                'hash': None,
                'changed': False
            }

            print(f"🔍 Searching for logos with {len(logo_selectors)} selectors...")

            for i, selector in enumerate(logo_selectors):
                logos = self.page.locator(selector)
                count = logos.count()
                print(f"   {i+1}. {selector}: {count} elements found")

                if count > 0:
                    # Check all matching elements, not just the first
                    for j in range(min(count, 3)):  # Check up to 3 elements
                        try:
                            current_logo_src = logos.nth(j).get_attribute('src')
                            if current_logo_src:
                                current_logo_hash = hashlib.md5(current_logo_src.encode()).hexdigest()
                                print(f"      Element {j+1}: {current_logo_src[:60]}...")
                                print(f"      Hash: {current_logo_hash}")

                                current_logo_info = {
                                    'found': True,
                                    'src': current_logo_src,
                                    'hash': current_logo_hash,
                                    'selector_used': f"{selector}:nth({j})"
                                }

                                # Compare with original immediately
                                if original_logo_info and original_logo_info.get('found'):
                                    if current_logo_hash != original_logo_info.get('hash'):
                                        current_logo_info['changed'] = True
                                        print(f"✅ Logo has changed! Found different hash")
                                        print(f"   Original: {original_logo_info.get('src', '')[:50]}...")
                                        print(f"   Current:  {current_logo_src[:50]}...")
                                        print(f"   Original hash: {original_logo_info.get('hash')}")
                                        print(f"   Current hash:  {current_logo_hash}")
                                        return current_logo_info
                        except Exception as e:
                            print(f"      Error checking element {j+1}: {e}")
                            continue

            # If we get here, no changed logo was found
            print("❌ No changed logo found after checking all selectors")

            # Return the last found logo info or empty info
            if 'current_logo_info' in locals():
                current_logo_info['changed'] = False
                print(f"❌ Logo appears UNCHANGED!")
                print(f"   Same hash as original")
                return current_logo_info
            else:
                print("❌ No current logo found for comparison")
                return {'found': False, 'changed': False}

        except Exception as e:
            print(f"❌ Error verifying logo change: {e}")
            return {'found': False, 'error': str(e), 'changed': False}

    def verify_uploaded_logo_visible(self):
        """Verify that an uploaded logo is visible on the page."""
        try:
            # Wait a moment for the page to update
            self.page.wait_for_timeout(2000)

            # Look for any images that might be the uploaded logo
            image_selectors = [
                "img[src*='blob:']",  # Blob URLs often used for uploaded files
                "img[src*='data:']",  # Data URLs for inline images
                "img[src*='upload']", # URLs containing 'upload'
                "img[src*='logo']",   # URLs containing 'logo'
                ".preview img",       # Images in preview areas
                "[data-testid*='preview'] img"
            ]

            for selector in image_selectors:
                images = self.page.locator(selector)
                if images.count() > 0:
                    # Check if the image is actually visible
                    if images.first.is_visible():
                        src = images.first.get_attribute('src')
                        print(f"✅ Uploaded logo is visible: {src[:50]}...")
                        return True

            # Alternative: Look for any new images that appeared
            all_images = self.page.locator("img")
            visible_count = 0
            for i in range(all_images.count()):
                if all_images.nth(i).is_visible():
                    visible_count += 1

            print(f"📊 Total visible images on page: {visible_count}")

            # Take screenshot for manual verification
            self.page.screenshot(path="logo_visibility_check.png")
            print("📸 Screenshot saved for manual verification")

            return visible_count > 0

        except Exception as e:
            print(f"❌ Error verifying uploaded logo visibility: {e}")
            return False

    def create_test_logo_file(self, file_path: str):
        """Create a report_button.png file for testing."""
        try:
            from PIL import Image, ImageDraw, ImageFont
            from datetime import datetime

            # Create a unique logo with timestamp
            img = Image.new('RGB', (200, 100), color='blue')
            draw = ImageDraw.Draw(img)

            # Add text to make the logo identifiable
            timestamp = datetime.now().strftime("%H:%M:%S")
            try:
                # Try to use a font, fallback to default if not available
                font = ImageFont.load_default()
                draw.text((10, 40), f"REPORT {timestamp}", fill='white', font=font)
            except:
                draw.text((10, 40), f"REPORT {timestamp}", fill='white')

            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # Save the image
            img.save(file_path, 'PNG')
            print(f"✓ Report button logo created: {file_path}")
            print(f"   Logo contains timestamp: {timestamp}")
            return True

        except ImportError:
            print("⚠️ PIL not available, using basic method")
            try:
                import base64

                # Basic PNG data
                png_data = base64.b64decode(
                    'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA'
                    'PgAAOw=='
                )

                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, 'wb') as f:
                    f.write(png_data)
                print(f"✓ Basic test logo created: {file_path}")
                return True

            except Exception as e:
                print(f"❌ Error creating basic logo: {e}")
                return False

        except Exception as e:
            print(f"❌ Error creating test logo: {e}")
            return False
