from playwright.sync_api import Page


class ProdLoginPage:
    """Page Object Model for Login functionality in Production environment."""

    # Locators - Using specific selectors instead of get_by_role
    USERNAME_INPUT = "input[name='username']"
    PASSWORD_INPUT = "input[name='password']"
    LOGIN_BUTTON = "button[type='submit']"
    
    # Alternative locators
    ALT_USERNAME_INPUT = "input[placeholder*='username'], input[placeholder*='Username'], #username"
    ALT_PASSWORD_INPUT = "input[placeholder*='password'], input[placeholder*='Password'], #password"
    ALT_LOGIN_BUTTON = "button:has-text('Login'), button:has-text('Sign In'), input[type='submit']"

    # Timeouts
    DEFAULT_TIMEOUT = 5000
    LOGIN_TIMEOUT = 10000

    def __init__(self, page: Page):
        self.page = page

    def login(self, username: str, password: str):
        """Login with provided credentials."""
        try:
            # Fill username
            username_input = self.page.locator(self.USERNAME_INPUT)
            if not username_input.is_visible(timeout=self.DEFAULT_TIMEOUT):
                username_input = self.page.locator(self.ALT_USERNAME_INPUT).first
            
            username_input.fill(username)
            print(f"✓ Username filled: {username}")
            
            # Fill password
            password_input = self.page.locator(self.PASSWORD_INPUT)
            if not password_input.is_visible(timeout=self.DEFAULT_TIMEOUT):
                password_input = self.page.locator(self.ALT_PASSWORD_INPUT).first
            
            password_input.fill(password)
            print("✓ Password filled")
            
            # Click login button
            login_button = self.page.locator(self.LOGIN_BUTTON)
            if not login_button.is_visible(timeout=self.DEFAULT_TIMEOUT):
                login_button = self.page.locator(self.ALT_LOGIN_BUTTON).first
            
            login_button.click()
            print("✓ Login button clicked")
            
            # Wait for login to complete
            self.page.wait_for_timeout(3000)
            
            # Verify login success (look for dashboard or settings elements)
            if self.verify_login_success():
                print("✅ Login successful")
            else:
                print("⚠️ Login verification inconclusive")
                
        except Exception as e:
            print(f"❌ Error during login: {e}")
            self.page.screenshot(path="login_error.png")
            raise

    def verify_login_success(self):
        """Verify that login was successful."""
        try:
            # Look for common post-login elements
            success_indicators = [
                "button:has-text('Settings')",
                "button:has-text('Dashboard')",
                ".dashboard",
                "[data-testid='main-navigation']",
                ".main-content",
                "nav",
                ".navbar"
            ]
            
            for indicator in success_indicators:
                if self.page.locator(indicator).is_visible(timeout=3000):
                    return True
                    
            return False
            
        except Exception as e:
            print(f"❌ Error verifying login: {e}")
            return False

    def navigate_to_login_page(self, url: str = None):
        """Navigate to the login page."""
        try:
            if url:
                self.page.goto(url)
            else:
                # Default login URL - adjust as needed
                self.page.goto("https://your-app-url.com/login")
            
            print("✓ Navigated to login page")
            
        except Exception as e:
            print(f"❌ Error navigating to login page: {e}")
            raise
