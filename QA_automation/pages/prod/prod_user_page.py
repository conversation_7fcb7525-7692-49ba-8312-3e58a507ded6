from playwright.sync_api import Page


class ProdUserPage:
    """Page Object Model for User Management functionality in Production environment."""

    # Locators - More robust selectors
    NEW_USER_BUTTON = "button:has-text('New user')"
    DROPDOWN_TRIGGER = ".MuiBox-root.css-uwwqev"
    SUPER_ADMIN_CHECKBOX = "[title='Super Admin']"
    CREATE_BUTTON = "button:has-text('Create')"
    UPDATE_BUTTON = "button:has-text('Update')"
    SEARCH_INPUT = "//div[@id='DcoyaUpgradedInput_search_bar']//input"

    # Form input locators - using the original working approach
    USERNAME_INPUT = "input[type='text']"
    FIRSTNAME_INPUT = "input[type='text']"
    LASTNAME_INPUT = "input[type='text']"
    EMAIL_INPUT = "input[type='text']"
    PASSWORD_INPUT = "input[type='password']"
    CONFIRM_PASSWORD_INPUT = "input[type='password']"

    # Edit form locators - more specific selectors
    EDIT_FIRSTNAME_INPUT = "input[name='firstName']"
    EDIT_LASTNAME_INPUT = "input[name='lastName']"
    EDIT_USERNAME_INPUT = "input[name='username']"

    # Navigation buttons - more specific
    PERMISSIONS_TAB = "button:has-text('Permissions')"
    SETTINGS_TAB = "button:has-text('Settings')"

    # Dynamic locators
    USER_ROW = "div[role='row']:has(div:text('{search_term}'))"
    USER_EDIT_BUTTON = "div[role='row']:has(div:text('{search_term}')) button:has(i.fa-pencil)"

    # Timeouts
    DEFAULT_TIMEOUT = 3000
    QUICK_TIMEOUT = 1000
    REDUCED_TIMEOUT = 2000
    FORM_LOAD_TIMEOUT = 5000

    def __init__(self, page: Page):
        self.page = page

    def create_user(self, username: str, firstname: str, lastname: str, email: str, password: str):
        """Create a new user using the helper methods for better organization."""
        self._click_new_user_button()
        self._fill_user_creation_form(username, firstname, lastname, email, password)
        self._set_super_admin_permissions()
        self._submit_user_creation()
        print("✓ User creation completed, waiting for page response")

    def edit_user(self, search_term: str, new_firstname: str, new_lastname: str):
        """Edit a user using helper methods for better organization."""
        try:
            self._select_user_checkbox(search_term)
            self._click_edit_button(search_term)
            self._fill_edit_form(new_firstname, new_lastname)
            self._submit_user_edit()
        except Exception as e:
            print(f"❌ User edit failed: {e}")
            raise

    def search_user(self, search_term: str):
        """Search for a user in the user list."""
        print(f"Searching for user: {search_term}")

        # Wait for UI to be ready
        self.page.wait_for_timeout(self.DEFAULT_TIMEOUT)
        print("✓ Waiting for UI to be ready for search")

        # Use the specific XPath selector for the search input
        search_box = self.page.locator(self.SEARCH_INPUT)

        # Clear any existing text and fill with search term
        search_box.clear()
        self.page.wait_for_timeout(self.QUICK_TIMEOUT)
        search_box.fill(search_term)
        print(f"✓ Searched for user: {search_term}")

        # Don't press Enter to avoid page refresh - let auto-search work
        print("✓ Search triggered (without Enter key)")

        # Wait for search results to load
        self.page.wait_for_timeout(4000)
        print("✓ Waited for search results")

    def verify_user_exists(self, search_term: str, username: str = None) -> bool:
        """Verify if a user exists in the current page content."""
        page_content = self.page.content()
        found = search_term.lower() in page_content.lower()

        if found:
            print(f"✅ User search completed - '{search_term}' found in page content")
            if username:
                print(f"✅ User creation verification completed for {username}")
        else:
            print(f"⚠️ '{search_term}' not found in page content after search")
            # Try to debug by checking what's in the search box
            try:
                search_box = self.page.locator(self.SEARCH_INPUT)
                search_value = search_box.input_value()
                print(f"🔍 Search box value: '{search_value}'")
            except Exception as e:
                print(f"🔍 Could not get search box value: {e}")

        return found

    def ensure_on_settings_page(self):
        """Ensure we're on the settings page and navigate if needed."""
        current_url = self.page.url
        print(f"✓ Current URL after user creation: {current_url}")

        # If page auto-refreshed or we're not on settings page, navigate back
        if "settings" not in current_url.lower():
            print("✓ Page auto-refreshed, navigating back to Settings")
            self.page.goto("https://admin.goninjio.com/react/settings")
            self.page.wait_for_timeout(self.REDUCED_TIMEOUT)

            # Click on Permissions tab - using more specific locator
            self.page.locator(self.PERMISSIONS_TAB).click()
            self.page.wait_for_timeout(self.REDUCED_TIMEOUT)

            # Click on Settings tab (needed for search to work) - using more specific locator
            self.page.locator(self.SETTINGS_TAB).click()
            self.page.wait_for_timeout(self.REDUCED_TIMEOUT)
            print("✓ Navigated back to Settings > Permissions > Settings for search")
        else:
            print("✓ Still on settings page, no need to navigate back")

    # Helper methods for cleaner code organization
    def _click_new_user_button(self):
        """Click the New User button."""
        self.page.locator(self.NEW_USER_BUTTON).click()

    def _fill_user_creation_form(self, username: str, firstname: str, lastname: str, email: str, password: str):
        """Fill the user creation form with provided data."""
        self.page.locator(self.USERNAME_INPUT).first.fill(username)
        self.page.locator(self.FIRSTNAME_INPUT).nth(1).fill(firstname)
        self.page.locator(self.LASTNAME_INPUT).nth(2).fill(lastname)
        self.page.locator(self.PASSWORD_INPUT).first.fill(password)
        self.page.locator(self.CONFIRM_PASSWORD_INPUT).nth(1).fill(password)
        self.page.locator(self.EMAIL_INPUT).nth(3).fill(email)

    def _set_super_admin_permissions(self):
        """Set Super Admin permissions for the user."""
        # Open dropdown
        self.page.locator(self.DROPDOWN_TRIGGER).click()

        # Select Super Admin checkbox using the constant
        self.page.locator(self.SUPER_ADMIN_CHECKBOX).click()

        # Click outside to close dropdown
        self.page.locator("body").click()

    def _submit_user_creation(self):
        """Submit the user creation form."""
        self.page.locator(self.CREATE_BUTTON).click()
        self.page.wait_for_timeout(self.DEFAULT_TIMEOUT)

    def _select_user_checkbox(self, search_term: str):
        """Select the checkbox for a specific user."""
        try:
            # Wait for the row containing the search term to appear
            row_locator = self.page.locator(self.USER_ROW.format(search_term=search_term)).first
            row_locator.wait_for(timeout=self.REDUCED_TIMEOUT)

            checkbox = row_locator.locator("input[type='checkbox']")
            checkbox.wait_for(timeout=self.QUICK_TIMEOUT)

            if checkbox.is_visible():
                checkbox.check()
                print(f"✓ Checkbox selected for user containing '{search_term}'")
            else:
                raise Exception("Checkbox exists but is not visible")

        except Exception as e:
            self._take_error_screenshot("error_checkbox_not_found.png")
            print(f"❌ Error trying to select checkbox: {e}")
            raise Exception(f"Could not select user checkbox: {e}")

    def _click_edit_button(self, search_term: str):
        """Click the edit button for a specific user."""
        print(f"✓ Current URL before clicking edit: {self.page.url}")

        edit_button = self.page.locator(self.USER_EDIT_BUTTON.format(search_term=search_term)).first
        edit_button.click()

        print("✓ Clicked edit button with pencil icon")

        # Wait for edit form to load
        self.page.wait_for_timeout(self.DEFAULT_TIMEOUT)
        print(f"✓ Current URL after clicking edit: {self.page.url}")

    def _fill_edit_form(self, new_firstname: str, new_lastname: str):
        """Fill the edit form with new user data."""
        try:
            # Wait for the first textbox to be available (keeping working approach)
            self.page.get_by_role("textbox").first.wait_for(timeout=self.FORM_LOAD_TIMEOUT)
            print("✓ Edit form loaded")

            self.page.get_by_role("textbox").nth(1).fill(new_firstname)
            print(f"✓ Filled firstname: {new_firstname}")

            self.page.get_by_role("textbox").nth(2).fill(new_lastname)
            print(f"✓ Filled lastname: {new_lastname}")

        except Exception as e:
            print(f"❌ Error filling form: {e}")
            self._take_error_screenshot("edit_form_error.png")
            raise Exception(f"Could not fill edit form: {e}")

    def _submit_user_edit(self):
        """Submit the user edit form."""
        self.page.locator(self.UPDATE_BUTTON).click()
        self.page.wait_for_timeout(self.REDUCED_TIMEOUT)
        print("✓ Update completed successfully")

    def _take_error_screenshot(self, filename: str):
        """Take a screenshot for debugging purposes."""
        self.page.screenshot(path=filename, full_page=True)
        print(f"✓ Screenshot saved: {filename}")


