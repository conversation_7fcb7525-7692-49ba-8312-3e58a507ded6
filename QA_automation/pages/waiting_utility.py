import platform
import time
from typing import List

from selenium.common import StaleElementReferenceException, \
    InvalidArgumentException
from selenium.common.exceptions import NoSuchElementException, TimeoutException
from selenium.webdriver import Keys
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait


class WaitingUtility:
    def __init__(self, driver, timeout=30, poll_frequency=5):
        self.driver = driver
        self.timeout = timeout
        self.poll_frequency = poll_frequency
        self.wait = WebDriverWait(driver, timeout, poll_frequency=poll_frequency,
                                  ignored_exceptions=[NoSuchElementException])

    def element_exists(self, by, value):
        """
        This method checks if an element exists on the page.

        :param by: The locator strategy to use.
        :param value: The locator value to use.
        :return: True if the element exists, False otherwise
        """
        try:
            self.driver.find_element(by, value)
            return True
        except NoSuchElementException:
            return False

    def wait_for_element(self, by, value):
        """
        This method waits for an element to be present.

        :param by: The locator strategy to use.
        :param value: The locator value to use.
        :return: The element if it is present. Otherwise, raises a TimeoutException.
        """
        return self.wait.until(EC.presence_of_element_located((by, value)))

    def wait_for_element_text(self, by, value, text):
        """
        This method waits for an element to be present,
        and for the text of the element to be equal to the provided text.

        :param by: The locator strategy to use.
        :param value: The locator value to use.
        :param text: The text to wait for.
        :return: The element if it is present. Otherwise, raises a TimeoutException.
        """
        return self.wait.until(EC.text_to_be_present_in_element((by, value), text))

    def wait_for_element_if_clickable_and_click(self, by, value):
        """
        This method waits for an element to be displayed and clickable.
        If it is clickable it performs a click.

        :param by: The locator strategy to use.
        :param value: The locator value to use.
        :return: True if clicked, False otherwise.
        """
        try:
            found_element = self.wait.until(EC.element_to_be_clickable((by, value)))
            found_element.click()
        except StaleElementReferenceException:
            return False
        else:
            return True

    def click_on_element_avoid_stale_element(self, by, value, retries=5):
        """
        This method waits for an element to be displayed and clickable.
        If it is clickable it performs a click.

        This method is expensive, should be avoided unless we are dealing with extensive re-rendering.

        :param by: The locator strategy to use.
        :param value: The locator value to use.
        :param retries: The number of times to retry if the element is not displayed.
        :return: True if clicked, False otherwise.
        """
        for _ in range(retries):
            element_clicked = self.wait_for_element_if_clickable_and_click(by, value)
            if element_clicked:
                return True
        return False

    def wait_for_input_send_keys_avoid_stale_element(self, by, value, keys, retries=5):
        """
        This method waits for an element to be displayed.
        If it is displayed it returns the element.

        This method is expensive, should be avoided unless we are dealing with extensive re-rendering.

        :param by: The locator strategy to use.
        :param value: The locator value to use.
        :param keys: The keys to send to the element.
        :param retries: The number of times to retry if the element is not displayed.
        :return: True if clicked, False otherwise.
        """
        for _ in range(retries):
            element = self.wait_for_element(by, value)
            try:
                if element is not None:
                    element.send_keys(keys)
                    return True
            except (StaleElementReferenceException, InvalidArgumentException) as e:
                print(f"while using wait_for_input_send_keys_avoid_stale_element: {e}")
                pass
        return False

    def wait_until_dcoya_table_loaded(self, table_id):
        """
        This method waits for a Dcoya table to load. The table is considered loaded when the table with id {
        table_id}_loaded is displayed.
        """
        try:
            loaded_table = self.wait.until(
                EC.presence_of_element_located((By.XPATH, f"//*[@id='{table_id}_loaded']")))
            return loaded_table.is_displayed()
        except TimeoutException as e:
            raise TimeoutException(f"Table with id {table_id} did not load in time.\n{e}")

    def wait_until_dcoya_system_button_not_in_cooldown(self, button_id):
        """
        This method waits for a Dcoya System Button to not be in cooldown. The button is considered not in cooldown when
        the button with id {button_id}_cooldown-false is displayed.

        :param button_id: The id of the button.
        :return: The button if it is displayed, otherwise raises a TimeoutException.
        """
        try:
            dcoya_system_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, f"//button[@id='{button_id}_cooldown-false']")))
            return dcoya_system_button
        except TimeoutException as e:
            raise TimeoutException(f"dcoya_system_button with id {button_id} did not load in time.\n{e}")

    def wait_until_template_grid(self, template_grid_id="template-grid", loading=False, expect_empty=False):
        """
        This method waits for a Template Grid to load. The Grid is considered loaded when the grid with id {
        template_grid_id}_loaded is displayed.

        :param template_grid_id: The id of the template grid.
        :param loading: If True, the method waits for the grid to be loading. If False, the method waits for the grid to
            be loaded.
        :param expect_empty: If True, the method waits for the grid to be empty.
        :return: True if the grid is displayed, False otherwise.
        """
        try:
            empty_str = "_empty" if expect_empty else ""
            loaded_grid = self.wait.until(
                EC.presence_of_element_located(
                    (By.XPATH, f"//*[@id='{template_grid_id}_load{'ing' if loading else f'ed{empty_str}'}']")))
            return loaded_grid.is_displayed()
        except TimeoutException as e:
            raise TimeoutException(
                f"Grid with id {template_grid_id} did not load in time. Requested loading: {loading} Expected Empty: {expect_empty}\n{e}")

    def wait_and_hover_on_element(self, by, value):
        """
        This method waits for an element to be present.
        :param by: The locator strategy to use.
        :param value: The locator value to use.
        :return: The element if it is present. Otherwise, returns None.
        """
        try:
            element = self.wait_for_element(by, value)
            ActionChains(self.driver).move_to_element(element).perform()
            return element
        except TimeoutException:
            return None

    def dropdown_click(self, dropdown_id, use_stale_element=False):
        """
        This method waits for a dropdown to be present and clicks on it.
        :param dropdown_id: The id of the dropdown.
        :param use_stale_element: If True, the method will try to click on the element avoiding a stale element.
        :return: True if the dropdown was clicked, False otherwise.
        """
        try:
            dropdown_xpath = f"//*[@id='{dropdown_id}_dcoya-drop-down-input']"

            if use_stale_element:
                self.click_on_element_avoid_stale_element(By.XPATH, dropdown_xpath)
            else:
                dropdown = self.wait.until(EC.element_to_be_clickable((By.XPATH, dropdown_xpath)))
                dropdown.click()
            return True
        except TimeoutException:
            return False

    def perform_search_in_dcoya_search_bar(self, search_keyword, search_input_id="search_bar", perform_clear=False,
                                           debounce=0.5):
        """
        This method waits for a Dcoya search input to be present and performs a search.
        :param search_keyword: The keyword to search.
        :param search_input_id: The id of the search input.
        :param perform_clear: If True, the method will clear the search input before sending the search keyword.
        :param debounce: The debounce time to wait after sending the search keyword.
        :return: True if the search was performed, False otherwise.
        """
        search_input = self.wait_for_element(
            *(By.XPATH, f"//div[@id='DcoyaUpgradedInput_{search_input_id}']//input"))
        if not search_input:
            return False

        # Detect the platform and use the appropriate key for "Select All"
        select_all_key = Keys.CONTROL if platform.system() != 'Darwin' else Keys.COMMAND  # Darwin is macOS

        if perform_clear:
            search_input.click()
            search_input.send_keys(select_all_key + 'a')  # Select all text (CTRL/COMMAND + 'A')
            search_input.send_keys(Keys.DELETE)  # Delete the selected text

        # Send the search keyword
        search_input.send_keys(search_keyword)
        # Small delay to handle search debounce
        time.sleep(debounce)
        return True

    def select_in_dcoya_multi_dropdown(self, dropdown_id, options: List[str], use_stale_element=False):
        """
        This method waits for a Dcoya multi dropdown to be present and selects the provided options.
        :param dropdown_id: The id of the dropdown.
        :param options: The options to select.
        :param use_stale_element: If True, the method will try to click on the element avoiding a stale element.
        :return: True if the option was selected, False otherwise.
        """

        try:
            dropdown_xpath = f"//*[@id='{dropdown_id}_dcoya-drop-down-input']"
            self.dropdown_click(dropdown_id, use_stale_element)
            for option in options:
                text_path = f"{dropdown_xpath}//li//div[text()='{option}']"

                # Scroll to the element
                self.perform_search_in_dcoya_search_bar(option, search_input_id="dcoya_dropdown_searchbar",
                                                        perform_clear=True, debounce=0.7)

                if use_stale_element:
                    self.click_on_element_avoid_stale_element(By.XPATH, text_path)
                else:
                    option_element = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, text_path)))
                    option_element.click()

            self.dropdown_click(dropdown_id, use_stale_element)

            return True
        except TimeoutException:
            return False

    def write_into_dropdown(self, dropdown_id, text, send_enter=False):
        """
        This method waits for a Dcoya dropdown to be present and writes the provided text.
        :param dropdown_id: The id of the dropdown.
        :param text: The text to write.
        :param send_enter: If True, the method will send an enter key after writing the text.
        :return: True if the text was written, False otherwise.
        """
        try:
            _xpath = f"//*[@id='{dropdown_id}_dcoya-drop-down-input']//input"
            self.click_on_element_avoid_stale_element(By.XPATH, _xpath)

            dropdown_inner_input = self.wait_for_element(By.XPATH, _xpath)
            dropdown_inner_input.send_keys(text)
            if send_enter:
                dropdown_inner_input.send_keys(Keys.ENTER)
            self.dropdown_click(dropdown_id)  # to close the dropdown
            return True
        except TimeoutException:
            return False

    def wait_for_element_to_appear(self, by, locator, timeout=None):

        timeout = timeout or self.timeout
        explicit_wait = WebDriverWait(self.driver, timeout)
        return explicit_wait.until(EC.presence_of_element_located((by, locator)))

    def hover_on_element_and_click_select(self, hover_element_locator, select_button_xpath, retries=3):
        """
        This method hovers on an element and clicks on a select button that appears.
        :param hover_element_locator: The XPath locator for the element to hover on
        :param select_button_xpath: The XPath locator for the select button to click
        :param retries: Number of retry attempts if the element is not found
        :return: True if the operation was successful, False otherwise
        """
        for attempt in range(retries):
            try:

                target_element = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, hover_element_locator))
                )

                action = ActionChains(self.driver)
                action.move_to_element(target_element).perform()

                button_wait = WebDriverWait(
                    self.driver,
                    timeout=5,
                    poll_frequency=0.2,
                    ignored_exceptions=[NoSuchElementException, StaleElementReferenceException]
                )

                select_button = button_wait.until(
                    EC.element_to_be_clickable((By.XPATH, select_button_xpath))
                )

                select_button.click()
                return True

            except (NoSuchElementException, TimeoutException, StaleElementReferenceException) as e:
                print(f"Error in hover_on_element_and_click_select (attempt {attempt + 1}/{retries}): {str(e)}")

                if attempt == retries - 1:
                    print(f"Failed to hover and click after {retries} attempts")
                    return False

        return False

    def wait_for_input_send_keys_to_search_bar(self, search_bar, keys, retries=5, clear_first=False):
        """
        This method explicitly waits for a search bar element to be displayed,
        clicks it, and sends keys with maximum reliability.

        :param search_bar: The ID of the search bar element.
        :param keys: The keys to send to the element.
        :param retries: The number of times to retry if the element is not displayed.
        :param clear_first: Whether to clear the input field before sending keys.
        :return: True if keys were successfully sent, False otherwise.
        """
        search_xpath = f"(//div[@id='DcoyaUpgradedInput_{search_bar}'])[1]"

        for attempt in range(retries):
            try:

                self.wait.until(EC.presence_of_element_located((By.XPATH, search_xpath)))

                self.wait.until(EC.visibility_of_element_located((By.XPATH, search_xpath)))

                element = self.wait.until(EC.element_to_be_clickable((By.XPATH, search_xpath)))

                action = ActionChains(self.driver)
                action.move_to_element(element).click().perform()

                if clear_first:

                    element.clear()

                    if element.get_attribute('value'):

                        select_all_key = Keys.CONTROL if platform.system() != 'Darwin' else Keys.COMMAND

                        select_action = ActionChains(self.driver)
                        select_action.key_down(select_all_key).send_keys('a').key_up(select_all_key).perform()

                        element.send_keys(Keys.DELETE)

                        if element.get_attribute('value'):
                            print(f"Warning: Could not clear search field on attempt {attempt + 1}")

                WebDriverWait(self.driver, 1).until(
                    lambda driver: element == driver.switch_to.active_element
                )

                for char in keys:
                    element.send_keys(char)

                entered_value = element.get_attribute('value')
                if keys in entered_value:
                    return True
                else:
                    print(f"Warning: Text verification failed. Expected '{keys}', got '{entered_value}'")

            except StaleElementReferenceException:
                print(f"Element became stale during attempt {attempt + 1}/{retries}")

            except TimeoutException:
                print(f"Timed out waiting for search bar during attempt {attempt + 1}/{retries}")

            except InvalidArgumentException as e:
                print(f"Invalid argument error during attempt {attempt + 1}/{retries}: {e}")

            except Exception as e:
                print(f"Unexpected error in attempt {attempt + 1}/{retries}: {str(e)}")

        return False
