import time
from time import sleep

from playwright.sync_api import Page

from base.logger import <PERSON><PERSON>ogger
from data.campaigns import Campaigns
from data.group_names import GroupNames
from data.simulations import Simulations



class SimulationPhishing:
    NEW_BUTTON = "newSimulationButton"
    NEXT_BUTTON = "//button[normalize-space()='Next']"
    SUBJECT_INPUT_SELECTOR = "#DcoyaUpgradedInput_subjectInput input"
    FROM_EMAIL_INPUT_SELECTOR = "#DcoyaUpgradedInput_fromEmailInput input"
    FROM_NAME_INPUT_SELECTOR = "#DcoyaUpgradedInput_sentFromInput input"
    DOMAINS_DROP_POPUP = f"//div[@id='domains-dropdown_{Simulations().SIMULATION_DROP_DOWN_NAME}']"
    SELECT_ALL_CHECKBOX = "(//div[normalize-space()='Select all'])[1]"
    DOMAINS_DROP_DOMAIN_SELECT = f"(//div[normalize-space()='{Simulations().SIMULATION_DOMAIN_NAME}'])[1]"
    SEARCH_GROUP_INPUT = "//div[@id='DcoyaUpgradedInput_search_bar']//input[1]"
    GROUP_NAME_CHECKBOX = f"//div[@title='{GroupNames().Group_NAME}']"
    FINISH_BUTTON = "(//button[normalize-space()='Finish'])"
    CREATE_CAMPAIGN_NAME = Campaigns.CAMPAIGN_NAME_PHISHING
    SELECT_CAMPAIGN = f"//div[contains(@aria-label, '{CREATE_CAMPAIGN_NAME}')]"
    CLONE_BUTTON="//button[normalize-space()='Clone']"

    def __init__(self, page: Page):
        self.page = page
        self.logger = QALogger("SimulationPhishingLogger").get_logger()

    def __click_new_button(self):
        try:
            new_button = self.page.locator(f"//button[@id='{self.NEW_BUTTON}_cooldown-false']")
            new_button.wait_for(state="visible", timeout=20000)
            new_button.click()
        except Exception as e:
            raise Exception(f"Click new button is not clickable: {e}")

    def __click_next_or_finish(self):
        # Try to click Next if present, otherwise click Finish
        try:
            next_button = self.page.locator(self.NEXT_BUTTON)
            if next_button.is_visible(timeout=2000):
                next_button.click()
                print("Clicked Next button")
                return
        except Exception:
            pass  # Next not found or not visible

        try:
            finish_button = self.page.locator(self.FINISH_BUTTON)
            if finish_button.is_visible(timeout=2000):
                finish_button.click()
                print("Clicked Finish button")
                return
        except Exception:
            pass  # Finish not found or not visible

        raise TimeoutError("Neither Next nor Finish button is clickable or visible.")

    def __input_subject_info(self, subject):
        try:
            subject_input = self.page.locator(self.SUBJECT_INPUT_SELECTOR)
            subject_input.wait_for(state="visible", timeout=10000)
            subject_input.fill(subject)
            print(f"Successfully filled subject: {subject}")
        except Exception as e:
            raise TimeoutError(f"Failed searching for subject or element is not found: {e}")

    def __input_from_email(self, email):
        try:
            email_input = self.page.locator(self.FROM_EMAIL_INPUT_SELECTOR)
            email_input.wait_for(state="visible", timeout=10000)
            email_input.fill(email)
            print(f"Successfully filled from email: {email}")
        except Exception as e:
            raise TimeoutError(f"Failed searching for email or element is not found: {e}")

    def __input_from_name(self, name):
        try:
            name_input = self.page.locator(self.FROM_NAME_INPUT_SELECTOR)
            name_input.wait_for(state="visible", timeout=10000)
            name_input.fill(name)
            print(f"Successfully filled from name: {name}")
        except Exception as e:
            raise TimeoutError(f"Failed searching for name or element is not found: {e}")

    def __click_domains_drop_down(self):
        try:
            domains_dropdown = self.page.locator(self.DOMAINS_DROP_POPUP)
            domains_dropdown.wait_for(state="visible", timeout=20000)
            domains_dropdown.click()
        except Exception as e:
            raise TimeoutError(f"Failed to open domains dropdown: {e}")

    def __click_select_all(self):
        try:
            select_all = self.page.locator(self.SELECT_ALL_CHECKBOX)
            select_all.wait_for(state="visible", timeout=20000)
            select_all.click()
        except Exception as e:
            raise TimeoutError(f"Failed to click select all checkbox: {e}")

    def clear_domain_list(self):
        self.__click_select_all()

    def __selected_domain(self):
        try:
            domain_select = self.page.locator(self.DOMAINS_DROP_DOMAIN_SELECT)
            domain_select.wait_for(state="visible", timeout=20000)
            domain_select.click()
        except Exception as e:
            raise TimeoutError(f"Failed to select the domain checkbox: {e}")

    def __exit_domains_drop_down(self):
        try:
            self.page.keyboard.press('Escape')
        except Exception as e:
            print(f"Error while sending ESC key: {e}")

    def __search_for_groups(self, group_name):
        try:
            # Find and click the search bar to activate it
            search_bar = self.page.locator(self.SEARCH_GROUP_INPUT)
            if search_bar.count() > 1:
                search_bar = search_bar.first
                print(f"Found {search_bar.count()} search inputs, using the first one")
            search_bar.wait_for(state="visible", timeout=20000)
            search_bar.click()
            # Clear any existing text and fill with the group name
            search_bar.fill("")
            search_bar.fill(group_name)
            # Wait for search results to load
            self.page.wait_for_timeout(2000)
            # Wait for the table to load (similar to the original wait_until_dcoya_table_loaded)
            # Try to find the group in the filtered results and click it
            group_locator = self.page.locator(self.GROUP_NAME_CHECKBOX)
            group_locator.wait_for(state="visible", timeout=10000)
            group_locator.click()
            print(f"Clicked on filtered group: {group_name}")
            # Click the Next button
            self.__click_next_or_finish()
            print("Clicked Next after selecting group")
        except Exception as e:
            raise Exception(f"FAILED Searching for: {self.GROUP_NAME_CHECKBOX} - {e}")

    def __click_in_search_group_checkbox(self):
        try:
            group_checkbox = self.page.locator(self.GROUP_NAME_CHECKBOX)
            group_checkbox.wait_for(state="visible", timeout=20000)
            group_checkbox.click()
            print(f"Successfully clicked group checkbox: {self.GROUP_NAME_CHECKBOX}")
        except Exception as e:
            raise TimeoutError(f"Checkbox of the search group is not clickable or element is not reachable: {e}")

    def __select_campaign(self):
        try:
            campaign_element = self.page.locator(self.SELECT_CAMPAIGN)
            campaign_element.wait_for(state="visible", timeout=20000)
            if campaign_element.is_visible():
                campaign_element.click()
            else:
                raise Exception("Campaign not found")
        except Exception as e:
            raise Exception(f"Campaign not found: {e}")

    def click_new_button(self):
        self.__click_new_button()

    def campaign_for_tested_simulation(self):
        try:
            self.__select_campaign()
        except Exception as e:
            print(f"Error selecting campaign, creating manually first: {e}")

    def domain_conf(self):
        self.__input_subject_info(subject=Simulations.SIMULATION_TRAINING_SUBJECT)
        self.__input_from_email(email=Simulations.SIMULATION_DOMAIN_NAME)
        self.__input_from_name(name=Simulations.SIMULATION_TRAINING_NAME)
        self.__click_domains_drop_down()
        self.__click_select_all()
        self.clear_domain_list()
        self.__selected_domain()
        time.sleep(3)  # TODO: temporary,till new dedicate waiting method will replace it
        self.__exit_domains_drop_down()
        self.__click_next_or_finish()

    def select_audience(self):
        self.__search_for_groups(group_name=GroupNames.Group_NAME)
        # Group is already selected and Next button is clicked in __search_for_groups

    def create_simulation(self):
        self.__click_next_or_finish()

    def create_new_simulation_phishing(self):
        print(f"Current URL: {self.page.url}")
        self.click_new_button()
        print("Clicked new button")
        self.__click_next_or_finish()
        print("Clicked first next")
        self.__click_next_or_finish()
        print("Clicked second next")
        self.domain_conf()
        print("Completed domain config")
        self.select_audience()
        print("Selected audience")
        self.create_simulation()
        print("Created simulation")


    def select_campaign_and_simulation(self):
        """Select the campaign and prepare for editing"""
        try:
            campaign_element = self.page.locator(self.SELECT_CAMPAIGN).first
            campaign_element.wait_for(state="visible", timeout=10000)
            if campaign_element.is_visible():
                campaign_element.click()
                print(f"Successfully selected campaign: {self.CREATE_CAMPAIGN_NAME}")
            else:
                raise Exception("Campaign not found")
        except Exception as e:
            raise Exception(f"Campaign not found: {e}")

        # Select the simulation that was created in the previous test
        try:
            # Look for the "New Simulation created on" aria-label
            simulation_element = self.page.locator('[aria-label*="New Simulation created on"]').first
            simulation_element.wait_for(state="visible", timeout=10000)
            simulation_element.click()
            print("Successfully selected the created simulation")

            # Small sleep to ensure the simulation is fully loaded
            self.page.wait_for_timeout(1000)

        except Exception as e:
            raise Exception(f"Failed to select simulation: {e}")





    def clone_simulation(self):
        try:
            # Ensure we are on the campaign list page
            self.page.goto("http://localhost/react/simulations")
            self.page.wait_for_load_state('networkidle')
            self.select_campaign_and_simulation()
            self.page.wait_for_timeout(3000)
            clone_button = self.page.locator(self.CLONE_BUTTON)
            clone_button.wait_for(state="visible", timeout=10000)
            clone_button.click()
            print("Successfully clicked the Clone button")
            self.page.wait_for_timeout(2000)
        except Exception as e:
            raise Exception(f"Failed to select simulation or click clone: {e}")



    def clone_simulation_phishing(self):
        try:
            self.clone_simulation()
            self.__click_next_or_finish()
            self.__click_next_or_finish()
            self.create_simulation()
            print("Cloned simulation")
        except Exception as e:
            raise Exception(f"Simulation cloned process failed: {e}")










