import time
from logging import Logger

from playwright.sync_api import Page, expect
from data.group_names import GroupNames
from data.users_info import UsersInfo


"""
Users and groups page divided to different use cases in Playwright POM methods
"""


class UsersAndGroups:

    def __init__(self, page: Page, logger: Logger):
        self.logger = logger
        self.page: Page = page
        self.employee_email_address = UsersInfo.email_address
        self.user_language = UsersInfo.language
        self.employee_first_name = UsersInfo.first_name
        self.employee_last_name = UsersInfo.last_name
        self.group_name = GroupNames.Group_NAME
        self.domain = UsersInfo.domain

        # Define locators for various elements in the web application
        self.click_on_New_user_button = "//button[normalize-space()='New user']"
        self.new_tab_button = "//button[normalize-space()='New']"
        self.group_name_field = "//*[@id='group-name']//input"
        self.create_button = "//button[normalize-space()='Create']"
        self.thanks_popup_message = "//button[normalize-space()='Thanks!']"
        self.select_group_drop_down_button = "//button[@id='groupsDropdownInEmployees_toggleDropdownButton_iconButton']"
        self.select_group_list = f"div[title='{GroupNames.Group_NAME}'] li[tabindex='-1']"
        self.import_customers_button = "//button[normalize-space()='Import']"
        self.upload_csv_button = "//p[normalize-space(text()) = 'Drop CSV file or click here to upload']"
        self.set_first_name = '//*[@id="user-input-First-name"]//input[1]'
        self.set_last_name = '//*[@id="user-input-Last-name"]//input[1]'
        self.set_email_address = '//*[@id="user-input-Email-address"]//input[1]'
        self.open_language_drop = "//div[@id='user_languages_dcoya-drop-down-input']"
        self.select_preferred_language = f"//div[contains(text(), {self.user_language})]"
        self.apply_button_new_user = "//button[normalize-space()='Apply']"
        self.add_users_icon = "//*[@id='batch-add-users-button_iconButton']"
        self.select_user = f"//div[@title='{self.employee_email_address}']"
        self.delete_user = "//button[normalize-space()='Delete']"
        self.delete_user_approve = ("//p[normalize-space(text()) = 'Delete 1 users?']//following::button["
                                    "normalize-space()='Confirm']")
        self.delete_group_btn = "//button[@id='groupDetails_deleteButton_iconButton']"
        self.delete_user_group_approve = (
            f"//p[contains(normalize-space(), \"You're going to delete the {GroupNames.Group_NAME}. Are you "
            f"sure?\")]/following::button[contains(normalize-space(), 'Delete')][2]"
        )
        self.search_user = "//*[@id='search-user']//input"
        self.search_user_send_text = "//*[@id='search-user']//input"
        self.add_user_search_box_click = "//*[@id='assign-user-search']//input"
        self.add_user_search_box = "//*[@id='assign-user-search']//input"
        self.log_history = "//button[@id='user-log-history_iconButton']"
        self.close_window_x = f"//button[@id='{self.domain}-new-modal-close-button_iconButton']"
        self.click_employee_details = "//button[@id='user-details_iconButton']"
        self.verify_user_activity_toggle_on = "//div[contains(text(), 'On')]"
        self.click_edit_pencil = "//button[@id='user-edit_iconButton']"
        self.edit_active_to_inactive = "//button[@id='active-user-switch_iconButton']"
        self.click_apply = ("//button[@id='apply-button_cooldown-false']//div["
                            "@class='MuiBox-root css-949aiz']")
        self.verify_user_inactive = "//button[@id='active-user-switch_iconButton']"

    # Click on the 'New' tab button
    def click_on_new_tab(self) -> None:
        self.page.locator(self.new_tab_button).click()

    # Set the group name field and enter the group name
    def set_group_name_field(self) -> None:
        group_name_field = self.page.locator(self.group_name_field)
        if group_name_field.is_visible():
            group_name_field.click()
            group_name_field.fill(GroupNames.Group_NAME)

    # Click on the 'Create' button
    def click_on_create(self) -> None:
        self.page.locator(self.create_button).click()

    # Click on the 'Thanks!' popup message
    def click_on_popup_approve(self) -> None:
        try:
            self.page.locator(self.thanks_popup_message).click(timeout=30000)
        except Exception as e:
            print(f"users_and_groups::click_on_popup_approve: Element is not clickable - {e}")

    # Create a new group by clicking on the new tab
    def create_group(self) -> None:
        self.click_on_new_tab()

    # Set the group name and create the group
    def set_groups_name(self) -> None:
        self.set_group_name_field()
        self.click_on_create()
        self.click_on_popup_approve()

    def select_group(self) -> None:
        try:
            self.page.locator(self.select_group_drop_down_button).click(timeout=30000)
            self.page.locator(self.select_group_list).click(timeout=30000)
        except Exception as e:
            raise TimeoutError(f"Group selection failed: {e}")

    # Click on the 'Import' button
    def click_on_import(self) -> None:
        import_customers = self.page.locator(self.import_customers_button)
        if import_customers.is_visible():
            import_customers.click()
        else:
            print("Element import is not clickable")

    # Click on the 'Upload CSV' button
    def click_on_upload(self) -> None:
        upload_button = self.page.locator(self.upload_csv_button)
        if upload_button.is_visible():
            upload_button.click()
        else:
            print("Element upload csv is not clickable")

    # Click on the 'New User' button
    def click_on_New_user(self) -> None:
        new_user_button = self.page.locator(self.click_on_New_user_button)
        if new_user_button.is_visible():
            new_user_button.click()
        else:
            print("Element is not clickable")

    # Set the first name of the new user
    def setting_first_name(self) -> None:
        first_name_input = self.page.locator(self.set_first_name)
        if first_name_input.is_visible():
            first_name_input.click()
            first_name_input.fill(self.employee_first_name)
        else:
            print("Element is not clickable")

    # Set the last name of the new user
    def setting_last_name(self) -> None:
        last_name_input = self.page.locator(self.set_last_name)
        if last_name_input.is_visible():
            last_name_input.click()
            last_name_input.fill(self.employee_last_name)
        else:
            print("Element is not clickable")

    # Set the email address of the new user
    def setting_email_address(self) -> None:
        email_input = self.page.locator(self.set_email_address)
        if email_input.is_visible():
            email_input.click()
            email_input.fill(self.employee_email_address)
        else:
            print("Element is not clickable")

    def open_user_language_drop_down(self) -> None:
        language_dropdown = self.page.locator(self.open_language_drop)
        if language_dropdown.is_visible():
            language_dropdown.click()
        else:
            print("User language dropdown is not clickable")

    def select_new_user_language(self) -> None:
        user_language_select = self.page.locator(self.select_preferred_language)
        if user_language_select.is_visible():
            user_language_select.click()
        else:
            print("User language dropdown is not clickable")

    # Click on the 'Apply' button for the new user
    def click_on_apply_new_user(self) -> None:
        apply_button = self.page.locator(self.apply_button_new_user)
        if apply_button.is_visible():
            apply_button.click()
        else:
            print("Element is not clickable")

    # Select a user from the list
    def user_select(self) -> None:
        try:
            self.page.locator(self.select_user).click(timeout=30000)
        except Exception as e:
            raise TimeoutError(f"users_and_groups::user_select: Element is not clickable - {e}")

    # Click on the 'Add Users' icon and apply user details
    def click_on_add_new_users(self) -> None:
        add_users = self.page.locator(self.add_users_icon)
        if add_users.is_visible():
            add_users.click()
            self.click_add_users_search_box()
            self.add_user_search()
            self.user_select()
            self.click_on_apply_new_user()
        else:
            print("Element is not clickable")

    # Delete a user and approve the deletion
    def delete_users(self) -> None:
        delete_user_button = self.page.locator(self.delete_user)
        if delete_user_button.is_visible():
            delete_user_button.click()
            approve_delete_button = self.page.locator(self.delete_user_approve)
            if approve_delete_button.is_visible():
                approve_delete_button.click()
            else:
                print("Element is not clickable")
        else:
            print("Element is not clickable")

    # Delete a group and approve the deletion
    def delete_groups(self) -> None:
        delete_user_group_button = self.page.locator(self.delete_group_btn)
        if delete_user_group_button.is_visible():
            delete_user_group_button.click()
            approve_delete_group_button = self.page.locator(self.delete_user_group_approve)
            if approve_delete_group_button.is_visible():
                approve_delete_group_button.click()
            else:
                print("Element is not clickable")
        else:
            print("Element is not clickable")

    # Click on the search box
    def click_search_box(self) -> None:
        user_search_box = self.page.locator(self.search_user)
        if user_search_box.is_visible():
            user_search_box.click()
        else:
            print("Element is not clickable")

    # Click on the log history button
    def click_log_history(self) -> None:
        try:
            self.page.locator(self.log_history).click(timeout=30000)
        except Exception as e:
            raise TimeoutError(f"Log History button is not clickable - {e}")

    # Close the window by clicking the "x" button
    def close_window_x_sign(self) -> None:
        try:
            self.page.locator(self.close_window_x).click(timeout=30000)
        except Exception as e:
            raise TimeoutError(f"Window close 'x' button is not clickable - {e}")

    # Click on the employee details button
    def click_employee_details_icon(self) -> None:
        try:
            self.page.locator(self.click_employee_details).click(timeout=30000)
        except Exception as e:
            raise TimeoutError(f"Employee details button is not clickable - {e}")

    # Close the employee details window by clicking the "x" button
    def close_employee_details_button(self) -> None:
        try:
            self.page.locator(self.close_window_x).click(timeout=30000)
        except Exception as e:
            raise TimeoutError(f"Close employee details button is not clickable - {e}")

    # Click on the edit pencil button to edit details
    def click_edit_pencil_icon(self) -> None:
        try:
            self.page.locator(self.click_edit_pencil).click(timeout=30000)
        except Exception as e:
            raise TimeoutError(f"Edit pencil button is not clickable - {e}")

    # Change the status from "Active" to "Inactive"
    def edit_active_to_inactive_toggle(self) -> None:
        try:
            self.page.locator(self.edit_active_to_inactive).click(timeout=30000)
        except Exception as e:
            raise TimeoutError(f"Failed to change active status to inactive - {e}")

    # Click the apply button to confirm changes
    def click_apply_button(self) -> None:
        try:
            self.page.locator(self.click_apply).click(timeout=30000)
        except Exception as e:
            raise TimeoutError(f"Apply button is not clickable - {e}")

    # Enter a search query in the search box
    def enter_search_query(self, query: str) -> None:
        user_search_box = self.page.locator(self.search_user_send_text)
        if user_search_box.is_visible():
            user_search_box.fill(query)
        else:
            print("Element is not clickable")

    def click_add_users_search_box(self) -> None:
        user_search_box = self.page.locator(self.add_user_search_box_click)
        if user_search_box.is_visible():
            user_search_box.click()
        else:
            print("Element is not clickable")

    def add_user_search(self) -> None:
        user_search_box = self.page.locator(self.add_user_search_box)
        if user_search_box.is_visible():
            user_search_box.fill(self.employee_email_address)
        else:
            print("Element is not clickable")

    def wait_for_table_to_load(self, table_id: str) -> bool:
        """Wait for a table to load completely"""
        try:
            self.page.wait_for_selector(".MuiDataGrid-root", timeout=30000)
            return True
        except Exception:
            return False

    def employee_activation_status(self):
        self.search_for_user()
        self.click_log_history()
        self.close_window_x_sign()
        self.click_employee_details_icon()
        self.close_employee_details_button()
        self.click_edit_pencil_icon()
        self.edit_active_to_inactive_toggle()
        self.click_apply_button()

    def new_group_create(self) -> None:
        """
        Create a new group and set its name
        """
        self.create_group()
        self.set_groups_name()
        time.sleep(1)
        self.page.reload()
        self.select_group()

    # Select an existing group
    def group_select(self) -> None:
        self.select_group()

    # Create a new group and add users to it
    def new_group_with_users(self) -> None:
        self.new_group_create()
        self.new_user_details()

    def add_users_csv(self) -> None:
        self.click_on_import()
        self.click_on_upload()

    def new_user_details(self) -> None:
        self.click_on_New_user()
        self.setting_first_name()
        self.setting_last_name()
        self.setting_email_address()
        self.open_user_language_drop_down()
        self.select_new_user_language()
        self.click_on_apply_new_user()
        self.click_on_popup_approve()

    def add_users(self) -> None:
        time.sleep(1)
        self.page.reload()
        self.select_group()
        self.click_on_add_new_users()

    def user_delete(self) -> None:
        self.click_search_box()
        self.enter_search_query(self.employee_email_address)
        if self.wait_for_table_to_load("users_management-users_list-main"):
            self.user_select()
            self.delete_users()
        else:
            raise TimeoutError("Table did not load")

    def search_for_user(self) -> None:
        self.click_search_box()
        self.enter_search_query(self.employee_email_address)
        if self.wait_for_table_to_load("users_management-users_list-main"):
            self.user_select()
        else:
            raise TimeoutError("Table did not load")

    def group_delete(self) -> None:
        self.select_group()
        self.delete_groups()

