import time
from logging import Logger
from playwright.sync_api import Page
from data.group_names import GroupNames

class PlaywrightPhishingReports:
    def __init__(self, page: Page, logger: Logger):
        self.logger = logger
        self.page = page
        self.timeout = 30000  # Playwright uses milliseconds

        # Locators
        self.open_menu_drop_down_button_click = "//button[@id='open_menu_iconButton']"
        self.create_button_click = "//button[@id='create_edit_cooldown-false']"
        self.next_button_click = "//button[@id='employee-learner-report-next-apply_cooldown-false']"
        self.search_group = '//*[@id="DcoyaUpgradedInput_search_bar"]//input'
        self.apply_button_click = "//button[@id='employee-learner-report-next-apply_cooldown-false']"
        self.done_button_click = "(//button[normalize-space()='Done'])"
        self.delete_report_button = "//button[@id='delete_cooldown-false']"
        self.approve_delete = ("//*[contains(text(), 'Do you want to delete this "
                              "report?')]//following::button[@id='system-button_cooldown-false'][2]")

    def click_on_open_menu_arrow(self) -> None:
        menu_button = self.page.locator(self.open_menu_drop_down_button_click)
        menu_button.wait_for(state="visible", timeout=self.timeout)
        menu_button.click(force=True)

    def click_on_create_button(self) -> None:
        create_button = self.page.locator(self.create_button_click)
        create_button.wait_for(state="visible", timeout=self.timeout)
        create_button.click(force=True)

    def click_on_next_button(self) -> None:
        """Click the Next button and wait for UI to update"""
        try:
            print("🔍 Looking for Next button...")
            next_button = self.page.locator(self.next_button_click)
            next_button.wait_for(state="visible", timeout=self.timeout)

            # Wait for button to be enabled
            self.page.wait_for_function(
                f"() => {{ const btn = document.querySelector('button[id=\"employee-learner-report-next-apply_cooldown-false\"]'); return btn && !btn.disabled; }}",
                timeout=self.timeout
            )

            next_button.click(force=True)
            print("✅ Next button clicked successfully")

            # Wait for UI to update after Next click
            self.page.wait_for_load_state('networkidle', timeout=5000)
            time.sleep(1)  # Additional wait for UI stabilization

        except Exception as e:
            print(f"❌ Next button click failed: {e}")
            raise TimeoutError(f"Next button is not responsive: {e}")

    def select_of_tested_group(self):
        search_input = self.page.locator(self.search_group).first
        search_input.wait_for(state="visible", timeout=self.timeout)
        search_input.click(force=True)
        search_input.fill(GroupNames().Group_NAME)

    def click_on_apply_button(self) -> None:
        """Click the Apply button - wait for button text to change to 'Apply'"""
        try:
            print("🔍 Looking for Apply button (waiting for text to change)...")

            # Wait for the button text to actually change to "Apply"
            try:
                self.page.wait_for_function(
                    "() => { const btn = document.querySelector('button[id=\"employee-learner-report-next-apply_cooldown-false\"]'); return btn && btn.textContent.includes('Apply'); }",
                    timeout=10000  # Wait up to 10 seconds for text to change
                )
                print("✅ Button text changed to 'Apply'")
            except:
                print("⚠️ Button text didn't change to 'Apply', but proceeding anyway...")

            apply_button = self.page.locator(self.apply_button_click)
            apply_button.wait_for(state="visible", timeout=self.timeout)

            # Wait for button to be enabled
            self.page.wait_for_function(
                f"() => {{ const btn = document.querySelector('button[id=\"employee-learner-report-next-apply_cooldown-false\"]'); return btn && !btn.disabled; }}",
                timeout=self.timeout
            )

            apply_button.click(force=True)
            print("✅ Apply button clicked successfully")

            # Add a small wait after Apply button click as requested in chat history
            print("⏳ Waiting after Apply button click...")
            self.page.wait_for_load_state('networkidle', timeout=5000)
            time.sleep(2)  # Small wait as requested

        except Exception as e:
            print(f"❌ Apply button click failed: {e}")
            raise TimeoutError(f"Apply button is not responsive: {e}")

    def click_on_done_button(self) -> None:
        try:
            print("🔍 Looking for Done button...")
            done_button = self.page.locator(self.done_button_click)
            done_button.wait_for(state="visible", timeout=self.timeout)

            # Scroll into view if needed
            done_button.scroll_into_view_if_needed()

            done_button.click(force=True)
            print("✅ Done button clicked successfully")

            # Wait for any final processing
            self.page.wait_for_load_state('networkidle', timeout=5000)

        except Exception as e:
            print(f"❌ Done button click failed: {e}")
            # Try alternative Done button locators
            try:
                print("🔄 Trying alternative Done button locators...")
                alternative_done_buttons = [
                    "//button[contains(text(), 'Done')]",
                    "//button[normalize-space()='Done']",
                    "//*[contains(text(), 'Done')]//ancestor::button[1]",
                    "//button[contains(@class, 'done')]",
                    "//input[@value='Done']"
                ]

                for locator in alternative_done_buttons:
                    try:
                        button = self.page.locator(locator)
                        if button.is_visible():
                            button.click(force=True)
                            print(f"✅ Clicked Done button using locator: {locator}")
                            return
                    except:
                        continue

                raise TimeoutError(f"Done button is not responsive with all methods: {e}")
            except Exception as e2:
                raise TimeoutError(f"Done button failed with all methods: {e2}")

    def click_on_delete_campaign_button(self) -> None:
        delete_button = self.page.locator(self.delete_report_button)
        delete_button.wait_for(state="visible", timeout=self.timeout)
        delete_button.click(force=True)

    def click_approve_delete(self) -> None:
        approve_button = self.page.locator(self.approve_delete)
        approve_button.wait_for(state="visible", timeout=self.timeout)
        approve_button.click(force=True)

    def select_audience(self):
        print("📋 Selecting audience...")
        self.select_of_tested_group()

        # First Next button
        print("⏭️ Clicking first Next button...")
        self.click_on_next_button()

        # Second Next button
        print("⏭️ Clicking second Next button...")
        self.click_on_next_button()

        # Apply button (without automatic Done click)
        print("✅ Clicking Apply button...")
        self.click_on_apply_button()

        # Small wait after Apply as requested
        print("⏳ Small wait after Apply button...")
        time.sleep(2)

        # Then click Done button
        print("✅ Clicking Done button...")
        self.click_on_done_button()

        print("✅ Audience selection completed!")

    def create_new_learner_report(self) -> None:
        self.click_on_open_menu_arrow()
        self.click_on_create_button()
        self.click_on_next_button()
        self.select_audience()

    def delete_report(self) -> None:
        self.click_on_open_menu_arrow()
        self.click_on_delete_campaign_button()
        self.click_approve_delete()

    def delete_learner_report(self) -> None:
        self.delete_report()