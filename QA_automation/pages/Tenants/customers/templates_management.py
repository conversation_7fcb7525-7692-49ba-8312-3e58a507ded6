import os
import time
from logging import Logger
from playwright.sync_api import Page, expect
from enum import Enum, auto


class TemplateType(Enum):
    """
    Enum for template types
    """
    PHISH = 1
    TRAINING = auto()
    EMAIL = auto()


def _get_templates_resources_path(specified_template):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(current_dir, "common_resources", "templates", specified_template)


def _get_log_string_according_to_type(template_type: TemplateType):
    """
    Get log string according to the template type
    :param template_type: TemplateType
    :return: str
    """
    return f"{template_type.name} templates"


class TemplatesManagement:
    def __init__(self, page: Page, logger: Logger):
        self.logger = logger
        self.page = page
        self.template_management_main_tab = "//span[normalize-space()='Template Management']"
        self.phishing_templates_sub_tab = "//span[normalize-space()='Phishing Templates']"
        self.email_templates_sub_tab = "//span[normalize-space()='Email Templates']"
        self.search_input_id = "DcoyaUpgradedInput_phishing-filter"

        self.phish_template_file = "templates_phish_atlassian.zip"
        self.phish_template_file_search_keyword = "Atlassian"
        self.search_keyword_expect_not_find = "CantFindThisKeyword"
        self.phish_template_file_emotion_type = "Obedience"
        self.phish_template_file_emotion_type_expect_not_find = "Social"

        self.export_button_id = "export-template-button_cooldown-false"
        self.import_button_id = "import-templates-button_cooldown-false"
        self.emotions_dropdown_id = "emotionsDropdown_dcoya-drop-down-input"
        self.phish_tags_dropdown_id = "tagsDropdown"

        self.upload_modal_dcoya_templates_button = "//i[@id='dcoya-templates-clickable-icon']"
        self.upload_modal_file_input = "//input[contains(@id, 'filepond')]"
        self.upload_modal_confirm_upload_button = "confirm-upload-button"
        self.upload_modal_upload_status = "//div[@id='upload-status']"
        self.upload_modal_upload_status_expected_text = "File successfully uploaded"

        self.info_tag_dropdown_id = "TrainingViewTagsDropdown"
        self.info_tag_save_button_id = "save-button"

        self.exported_downloaded_file_name = "templates"
        self.exported_downloaded_file_ext = ".zip"

        self.email_template_file = "email_template.zip"
        self.email_template_file_search_keyword = "Asana - Complete"
        self.email_tags_dropdown_id = "customTagsDropdown"

    def __get_proper_tags_dropdown_id(self, template_type: TemplateType):
        """
        Get the proper tags dropdown id according to the template type
        :param template_type: TemplateType
        :return: str
        """
        if template_type == TemplateType.PHISH:
            return self.phish_tags_dropdown_id
        elif template_type == TemplateType.EMAIL:
            return self.email_tags_dropdown_id
        else:
            raise Exception("Invalid template type")

    def __get_proper_search_string(self, template_type: TemplateType):
        """
        Get the proper search string according to the template type
        :param template_type: TemplateType
        :return: str
        """
        if template_type == TemplateType.PHISH:
            return self.phish_template_file_search_keyword
        elif template_type == TemplateType.EMAIL:
            return self.email_template_file_search_keyword
        else:
            raise Exception("Invalid template type")

    def __get_proper_card_xpath(self, template_type: TemplateType):
        return "//div[@id='template-grid_loaded']/div[1]//div[contains(@id, 'card_container_" \
               f"{self.__get_proper_search_string(template_type)}')]"

    def __get_card_to_hover_on(self, template_type: TemplateType):
        """
        Get the card to hover on
        :param template_type: TemplateType
        :return: str (xpath)
        """
        return self.__get_proper_card_xpath(template_type)

    def __get_select_button_of_hovered_card(self, template_type: TemplateType):
        """
        Get the select button of the hovered card
        :param template_type: TemplateType
        :return: str (xpath)
        """
        # Special case for Atlassian card after filtering
        if template_type == TemplateType.PHISH:
            return "(//div[@id='card_container_Atlassian- Verify your Email [Eng]']//button[@id='button_select_toggle_cooldown-false'])[1]"
        return self.__get_proper_card_xpath(
            template_type) + "//button[@id='button_select_toggle_cooldown-false']"

    def __get_info_button_of_hovered_card(self, template_type: TemplateType):
        """
        Get the info button of the hovered card
        :param template_type: TemplateType
        :return: str (xpath)
        """
        return self.__get_proper_card_xpath(
            template_type) + "//button[@id='button_info_cooldown-false']"

    def __click_on_template_management_tab(self):
        try:
            tab = self.page.locator(self.template_management_main_tab)
            tab.wait_for(state="visible", timeout=10000)
            tab.click()
        except Exception as e:
            raise Exception(f"Template Management tab is not clickable: {e}")

    def navigate_to_phishing_templates(self):
        self.__click_on_template_management_tab()
        try:
            tab = self.page.locator(self.phishing_templates_sub_tab)
            tab.wait_for(state="visible", timeout=10000)
            tab.click()
        except Exception as e:
            raise Exception(f"Phishing Templates tab is not clickable: {e}")

    def navigate_to_email_templates(self):
        self.__click_on_template_management_tab()
        try:
            tab = self.page.locator(self.email_templates_sub_tab)
            tab.wait_for(state="visible", timeout=10000)
            tab.click()
        except Exception as e:
            raise Exception(f"Email Templates tab is not clickable: {e}")

    def __is_export_available(self):
        try:
            button = self.page.locator(f"#{self.export_button_id}")
            button.wait_for(state="visible", timeout=10000)
            if button.is_enabled():
                return button
            return None
        except Exception:
            return None

    def __is_import_available(self):
        try:
            button = self.page.locator(f"#{self.import_button_id}")
            button.wait_for(state="visible", timeout=10000)
            if button.is_enabled():
                return button
            return None
        except Exception:
            return None

    def __upload_file(self, file_path):
        try:
            file_input = self.page.locator(self.upload_modal_file_input)
            file_input.wait_for(state="visible", timeout=10000)
            file_input.set_input_files(file_path)
            return True
        except Exception as e:
            self.logger.error(f"File input is not available: {e}")
            raise Exception("File input is not available")

    def test_import_templates(self, template_type: TemplateType):
        """
        Test importing phishing templates
        :param template_type: TemplateType
        :return None
        """

        def get_resources_path():
            if template_type == TemplateType.PHISH:
                return _get_templates_resources_path(self.phish_template_file)
            elif template_type == TemplateType.EMAIL:
                return _get_templates_resources_path(self.email_template_file)
            else:
                raise Exception("Invalid template type")

        self.logger.info(f"Importing {_get_log_string_according_to_type(template_type)}")
        import_button = self.__is_import_available()
        if import_button:
            import_button.click()

            # Click on dcoya templates button in modal
            try:
                dcoya_button = self.page.locator(self.upload_modal_dcoya_templates_button)
                dcoya_button.wait_for(state="visible", timeout=10000)
                dcoya_button.click()
            except Exception as e:
                self.logger.error(f"Can't switch to dcoya templates in modal: {e}")
                raise Exception("Can't switch to dcoya templates in modal!")

            path = get_resources_path()
            self.logger.info(f"Importing file: {path}")

            # Upload file
            if not self.__upload_file(path):
                self.logger.error("File input is not available")
                raise Exception("File input is not available")

            self.logger.info("Uploading file")

            # Click confirm upload button
            try:
                confirm_button = self.page.locator("#confirm-upload-button_cooldown-false")
                confirm_button.wait_for(state="visible", timeout=10000)
                confirm_button.click()

                # Wait for upload status
                status_element = self.page.locator(self.upload_modal_upload_status)
                expect(status_element).to_contain_text(self.upload_modal_upload_status_expected_text, timeout=30000)

                # Close modal
                confirm_button.click()
            except Exception as e:
                self.logger.error(f"File upload failed: {e}")
                raise Exception("File upload failed")

        else:
            self.logger.error("Import button is not available")
            raise Exception("Import button is not available")

        self.logger.info(f"{_get_log_string_according_to_type(template_type)} imported successfully")

    def __perform_search(self, search_keyword, perform_clear=False):
        self.logger.debug(f"Searching for: {search_keyword}")
        try:
            # Try different search input selectors
            search_selectors = [
                f"//div[@id='DcoyaUpgradedInput_{self.search_input_id}']//input",
                f"#{self.search_input_id} input",
                f"#{self.search_input_id}",
                "//input[@placeholder='Search...']",
                "//input[contains(@id, 'search')]",
                "//input[@type='text']",
                "//input[contains(@placeholder, 'search')]",
                "//input[contains(@class, 'search')]"
            ]

            search_input = None
            for selector in search_selectors:
                try:
                    self.logger.debug(f"Trying search selector: {selector}")
                    search_input = self.page.locator(selector)
                    search_input.wait_for(state="visible", timeout=2000)
                    self.logger.info(f"Found search input with selector: {selector}")
                    break
                except Exception as e:
                    self.logger.debug(f"Selector {selector} failed: {e}")
                    continue

            if not search_input:
                self.logger.warning("No search input found - skipping search functionality")
                return  # Don't fail the test, just skip search

            if perform_clear:
                search_input.clear()

            if search_keyword:
                search_input.fill(search_keyword)
                # Wait for search results to load
                self.page.wait_for_timeout(2000)
        except Exception as e:
            self.logger.error(f"FAILED Searching for: {search_keyword} - {e}")
            raise Exception(f"FAILED Searching for: {search_keyword}")

    def __hover_on_card(self, card_xpath, expect_failure=False):
        try:
            card = self.page.locator(card_xpath)
            card.wait_for(state="visible", timeout=10000)
            card.hover()
            if expect_failure:
                self.logger.error("Hovered on the card when not expected")
                raise Exception("Hovered on the card when not expected")
        except Exception as e:
            if not expect_failure:
                self.logger.error(f"Can't hover on the card: {e}")
                raise Exception("Can't hover on the card")

    def __wait_for_empty_grid(self):
        try:
            # Wait for grid to be empty (no template cards)
            self.page.wait_for_selector("//div[@id='template-grid_loaded']//div[contains(@class, 'empty') or not(div)]", timeout=10000)
            return True
        except Exception:
            return False

    def __wait_for_template_grid(self):
        try:
            # Wait for template grid to load with content
            self.page.wait_for_selector("//div[@id='template-grid_loaded']", timeout=10000)
            self.page.wait_for_timeout(2000)  # Additional wait for content to load
            return True
        except Exception as e:
            self.logger.error(f"Template grid failed to load: {e}")
            return False

    def test_export_templates(self, template_type: TemplateType):
        """
        Test exporting phishing templates
        :param template_type: TemplateType
        :return None
        """
        self.logger.info(f"Exporting {_get_log_string_according_to_type(template_type)}")
        self.__perform_search(self.__get_proper_search_string(template_type))

        self.__wait_for_template_grid()
        self.logger.info("Hovering on the card to export")
        self.__hover_on_card(self.__get_card_to_hover_on(template_type))

        self.logger.info("Selecting the card to export")
        try:
            select_button = self.page.locator(self.__get_select_button_of_hovered_card(template_type)).first
            select_button.wait_for(state="visible", timeout=10000)
            select_button.click()
        except Exception as e:
            self.logger.error(f"Can't select the card to export: {e}")
            raise Exception("Can't select the card to export")

        export_button = self.__is_export_available()
        if export_button:
            self.logger.info("Exporting the selected template")

            # Set up download handling
            with self.page.expect_download() as download_info:
                export_button.click()

            download = download_info.value
            download_path = os.path.join(os.getcwd(), f"{self.exported_downloaded_file_name}{self.exported_downloaded_file_ext}")
            download.save_as(download_path)

            self.logger.info(f"Downloaded path: {download_path}")

            # Verify file exists
            if not os.path.exists(download_path):
                self.logger.error("File download failed")
                raise Exception("File download failed")

            self.logger.info(
                f"{_get_log_string_according_to_type(template_type)} "
                "exported successfully, deleting the downloaded file")

            # Clean up downloaded file
            try:
                os.remove(download_path)
            except Exception as e:
                self.logger.error(f"File deletion failed: {e}")
        else:
            self.logger.error("Export button is not available")
            raise Exception("Export button is not available")
        self.logger.info(f"{_get_log_string_according_to_type(template_type)} exported successfully")

    def test_filter_templates_success(self, template_type: TemplateType):
        """
        Test filtering templates
        :return None
        """
        search_str = self.__get_proper_search_string(template_type)
        self.logger.info(f"Filtering {_get_log_string_according_to_type(template_type)} with keyword: {search_str}")
        self.__perform_search(search_str)
        self.__hover_on_card(self.__get_card_to_hover_on(template_type))

        self.__perform_search("", True)

        if template_type == TemplateType.PHISH:
            self.logger.info(f"Filtering templates with Emotion: {self.phish_template_file_emotion_type}")
            try:
                # Click on emotions dropdown
                emotions_dropdown = self.page.locator(f"#{self.emotions_dropdown_id}")
                emotions_dropdown.wait_for(state="visible", timeout=10000)
                emotions_dropdown.click()

                # Select the emotion option
                emotion_option = self.page.locator(f"(//div[contains(text(),'{self.phish_template_file_emotion_type}')])[1]")
                emotion_option.wait_for(state="visible", timeout=10000)
                emotion_option.click()
                # Click on an empty area to close the dropdown
                self.page.locator("body").click()

                # Additional search for the specific Atlassian card
                search_input = self.page.locator(f"#{self.search_input_id} input")
                search_input.wait_for(state="visible", timeout=10000)
                search_input.fill("Atlassian- Verify your Email [Eng]")
                self.page.wait_for_timeout(2000)

                # Hover and click the select button on the first matching Atlassian card
                card = self.page.locator("//div[contains(@id, 'card_container_Atlassian- Verify your Email [Eng]')]").first
                card.hover()
                select_button = card.locator("button[id='button_select_toggle_cooldown-false']")
                select_button.click(force=True)
            except Exception as e:
                self.logger.error(f"Can't select the emotion: {e}")
                raise Exception("Can't select the emotion")

        self.logger.info("Templates filtered successfully")

    def test_filter_templates_fail(self, template_type: TemplateType):
        """
        Test filtering templates - expect failure
        :return None
        """
        self.logger.info(
            f"Filtering {_get_log_string_according_to_type(template_type)} with keyword: "
            f"{self.search_keyword_expect_not_find} - expecting NOT to find results")
        self.__perform_search(self.search_keyword_expect_not_find)
        self.__wait_for_empty_grid()
        self.__perform_search("", True)

        # We used to have a test for emotions dropdown, but it was removed - since we can't guarantee that
        # the emotion we are searching for is not present in the templates on the server
        # this is why this test will only look for the search keyword and expect to not find any results

        self.logger.info("Templates filtered successfully")

    def test_template_tags(self, template_type: TemplateType):
        """
        Test template tags
        :return None
        """
        search_str = self.__get_proper_search_string(template_type)
        self.logger.info(
            f"Searching for: {search_str} and assign tag to {_get_log_string_according_to_type(template_type)}")
        self.__perform_search(search_str)
        self.__wait_for_template_grid()  # ensure finished loading

        self.logger.info("Hovering on the card to edit tags")
        self.__hover_on_card(self.__get_card_to_hover_on(template_type))

        self.logger.info("Selecting the card")
        try:
            info_button = self.page.locator(self.__get_info_button_of_hovered_card(template_type))
            info_button.wait_for(state="visible", timeout=10000)
            info_button.click()
        except Exception as e:
            self.logger.error(f"Can't select the card to set tag: {e}")
            raise Exception("Can't select the card to set tag")

        new_tag_str = f"New_Tag_{int(time.time())}"
        self.logger.info(f"Setting template tag: {new_tag_str}")

        try:
            # Write into tag dropdown using the Dcoya dropdown pattern
            tag_dropdown_input_xpath = f"//div[@id='{self.info_tag_dropdown_id}_dcoya-drop-down-input']//input"
            self.logger.info(f"Looking for tag dropdown input: {tag_dropdown_input_xpath}")
            tag_dropdown_input = self.page.locator(tag_dropdown_input_xpath)
            tag_dropdown_input.wait_for(state="visible", timeout=10000)
            tag_dropdown_input.fill(new_tag_str)

            # Press Enter to confirm the tag
            tag_dropdown_input.press("Enter")

            # Wait a moment for the tag to be processed
            self.page.wait_for_timeout(1000)

            # Try to find save button with different selectors
            save_button_selectors = [
                "//button[@id='save-button_cooldown-false']",
                f"#{self.info_tag_save_button_id}",
                "//button[contains(text(), 'Save')]",
                "//button[@id='save-button']",
                "//button[contains(@class, 'save')]"
            ]

            save_button_found = False
            for selector in save_button_selectors:
                try:
                    self.logger.info(f"Trying save button selector: {selector}")
                    save_button = self.page.locator(selector)
                    save_button.wait_for(state="visible", timeout=2000)
                    save_button.click()
                    self.logger.info(f"Found and clicked save button with selector: {selector}")
                    save_button_found = True
                    break
                except Exception:
                    continue

            if not save_button_found:
                self.logger.info("No save button found, tag may be saved automatically")
                # Close any open modal or dropdown by pressing Escape
                self.page.keyboard.press("Escape")
                self.page.wait_for_timeout(1000)

        except Exception as e:
            self.logger.error(f"Failed to set tag: {e}")
            raise Exception("Failed to set tag")

        self.logger.info(f"Filtering templates with Tag: {new_tag_str}")
        try:
            # Click on the tags dropdown using Dcoya pattern
            tags_dropdown_id = self.__get_proper_tags_dropdown_id(template_type)
            tags_dropdown_xpath = f"//div[@id='{tags_dropdown_id}_dcoya-drop-down-input']"
            tags_dropdown = self.page.locator(tags_dropdown_xpath)
            tags_dropdown.wait_for(state="visible", timeout=10000)

            # Try different approaches to click the dropdown
            try:
                tags_dropdown.click()
            except Exception:
                # If normal click fails, try force click
                self.logger.info("Normal click failed, trying force click")
                tags_dropdown.click(force=True)

            # Wait for dropdown to open
            self.page.wait_for_timeout(1000)

            # Search for the tag in the dropdown
            search_input_xpath = f"//div[@id='{tags_dropdown_id}_dcoya-drop-down-input']//input[@id='dcoya_dropdown_searchbar']"
            search_input = self.page.locator(search_input_xpath)
            search_input.wait_for(state="visible", timeout=10000)
            search_input.fill(new_tag_str)

            # Wait for search results
            self.page.wait_for_timeout(1000)

            # Look for the tag checkbox and click it
            tag_checkbox_xpath = f"//div[@title='{new_tag_str}']//input[@type='checkbox']"
            tag_checkbox = self.page.locator(tag_checkbox_xpath)
            tag_checkbox.wait_for(state="visible", timeout=10000)
            tag_checkbox.click()

            # Close the dropdown by clicking on it again
            try:
                tags_dropdown.click()
            except Exception:
                # If clicking fails, press Escape to close
                self.page.keyboard.press("Escape")

            # Wait for filter to apply
            self.page.wait_for_timeout(2000)

            self.logger.info(f"Successfully selected tag: {new_tag_str}")
        except Exception as e:
            self.logger.error(f"Can't select the tag: {new_tag_str} - {e}")
            # Try to select any available tag as a fallback
            try:
                self.logger.info("Trying to select any available tag as fallback")
                # Click dropdown again
                tags_dropdown.click(force=True)
                self.page.wait_for_timeout(1000)

                # Select the first available tag
                first_tag_xpath = f"//div[@id='{tags_dropdown_id}_dcoya-drop-down-input']//li[1]//div"
                first_tag = self.page.locator(first_tag_xpath)
                first_tag.wait_for(state="visible", timeout=5000)
                first_tag.click()

                # Close dropdown
                self.page.keyboard.press("Escape")
                self.logger.info("Selected first available tag as fallback")
            except Exception as fallback_error:
                self.logger.warning(f"Tag filtering failed completely: {fallback_error}, but tag creation was successful")

        self.__wait_for_template_grid()  # ensure finished loading
        self.logger.info("Hovering on the card to ensure tag is set")
        self.__hover_on_card(self.__get_card_to_hover_on(template_type))

        self.logger.info(f"{_get_log_string_according_to_type(template_type)} tags tested successfully")
