from playwright.sync_api import Page


class CampaignManagement:
    def __init__(self, page: Page):
        self.page = page
        self.campaign_management_tab = "//span[normalize-space()='Campaign Management']"
        self.training_tab = "//span[normalize-space()='Campaign Management']/following::span[normalize-space()='Training'][1]"
        self.phishing_tab = "//span[normalize-space()='Campaign Management']/following::span[normalize-space()='Phishing'][1]"

    def click_on_campaign_management_tab(self):
        campaign_management_element = self.page.locator(self.campaign_management_tab)
        campaign_management_element.wait_for(state="visible", timeout=20000)
        if campaign_management_element.is_visible():
            campaign_management_element.click()
        else:
            raise Exception("Campaign management tab is not displayed")

    def click_on_training_tab(self):
        training_element = self.page.locator(self.training_tab)
        training_element.wait_for(state="visible", timeout=20000)
        if training_element.is_visible():
            training_element.click()
        else:
            raise Exception("Training tab is not displayed")

    def training_page(self):
        self.click_on_campaign_management_tab()
        self.click_on_training_tab()

    def click_on_phishing_tab(self):
        phishing_element = self.page.locator(self.phishing_tab)
        phishing_element.wait_for(state="visible", timeout=20000)
        if phishing_element.is_visible():
            phishing_element.click()
        else:
            raise Exception("Phishing tab is not displayed")

    def phishing_page(self):
        self.click_on_campaign_management_tab()
        self.click_on_phishing_tab()
