import time

from playwright.sync_api import Page

from base.logger import <PERSON><PERSON>ogger
from data.campaigns import Campaigns
from data.group_names import GroupNames
from data.simulations import Simulations


class EditSimulationPhishing:
    def __init__(self, page: Page):
        self.page = page
        self.logger = QALogger("EditSimulationPhishingLogger").get_logger()

        # Locators for selecting campaign and simulation
        self.CREATE_CAMPAIGN_NAME = Campaigns.CAMPAIGN_NAME_PHISHING
        self.SELECT_CAMPAIGN = f"//div[contains(@aria-label, '{self.CREATE_CAMPAIGN_NAME}')]"
        
        # Locators for editing existing simulation
        self.EDIT_BUTTON = "//button[normalize-space()='Edit']"
        self.SIMULATION_ROW = "//tr[contains(@class, 'simulation-row')]"
        self.SIMULATION_ACTIONS_MENU = "//button[contains(@class, 'actions-menu')]"
        self.EDIT_MENU_ITEM = "//div[normalize-space()='Edit']"
        self.SAVE_BUTTON = "//button[normalize-space()='Save']"
        self.CANCEL_BUTTON = "//button[normalize-space()='Cancel']"
        self.UPDATE_BUTTON = "//button[normalize-space()='Update']"
        self.SEARCH_GROUP_INPUT = "//div[@id='DcoyaUpgradedInput_search_bar']//input[1]"
        self.GROUP_NAME_CHECKBOX = f"//div[@title='{GroupNames.EDITED_GROUP_NAME}']"

        # Form field selectors for editing
        self.SUBJECT_INPUT_SELECTOR = "#DcoyaUpgradedInput_subjectInput input"
        self.FROM_EMAIL_INPUT_SELECTOR = "#DcoyaUpgradedInput_fromEmailInput input"
        self.FROM_NAME_INPUT_SELECTOR = "#DcoyaUpgradedInput_sentFromInput input"
        self.SIMULATION_NAME_INPUT = 'simulationNameInput'
        self.SIMULATION_DESCRIPTION_INPUT = 'simulationDescriptionInput'
        
        # Navigation buttons
        self.NEXT_BUTTON = "//button[normalize-space()='Next']"
        self.FINISH_BUTTON = "(//button[normalize-space()='Finish'])"

    def _select_campaign(self):
        """Select the campaign created in the previous test"""
        try:
            campaign_element = self.page.locator(self.SELECT_CAMPAIGN).first
            campaign_element.wait_for(state="visible", timeout=10000)
            if campaign_element.is_visible():
                campaign_element.click()
                print(f"Successfully selected campaign: {self.CREATE_CAMPAIGN_NAME}")
            else:
                raise Exception("Campaign not found")
        except Exception as e:
            raise Exception(f"Campaign not found: {e}")



    def select_campaign_and_simulation(self):
        """Select the campaign and prepare for editing"""
        self._select_campaign()
        
        # Select the simulation that was created in the previous test
        try:
            # Look for the "New Simulation created on" aria-label
            simulation_element = self.page.locator('[aria-label*="New Simulation created on"]').first
            simulation_element.wait_for(state="visible", timeout=10000)
            simulation_element.click()
            print("Successfully selected the created simulation")
            
            # Small sleep to ensure the simulation is fully loaded
            self.page.wait_for_timeout(1000)
            
            # Click the second Edit button (index 2)
            edit_button_2 = self.page.locator("(//button[normalize-space()='Edit'])[2]")
            edit_button_2.wait_for(state="visible", timeout=10000)
            edit_button_2.click()
            print("Successfully clicked the second Edit button")
            # Wait a bit to allow the edited screen to appear
            self.page.wait_for_timeout(2000)
        except Exception as e:
            raise Exception(f"Failed to select simulation or click second edit: {e}")

    def __click_next_or_finish(self):
        # Try to click Next if present, otherwise click Finish
        try:
            next_button = self.page.locator(self.NEXT_BUTTON)
            if next_button.is_visible(timeout=2000):
                next_button.click()
                print("Clicked Next button")
                return
        except Exception:
            pass  # Next not found or not visible

        try:
            finish_button = self.page.locator(self.FINISH_BUTTON)
            if finish_button.is_visible(timeout=2000):
                self.page.wait_for_timeout(500)  # Small wait to let UI settle
                finish_button.click(force=True)
                print("Clicked Finish button (force=True)")
                return
        except Exception:
            pass  # Finish not found or not visible

        raise TimeoutError("Neither Next nor Finish button is clickable or visible.")

    def __input_subject_info(self, subject):
        try:
            subject_input = self.page.locator(self.SUBJECT_INPUT_SELECTOR)
            subject_input.wait_for(state="visible", timeout=10000)
            subject_input.fill(subject)
            print(f"Successfully filled subject: {subject}")
        except Exception as e:
            raise TimeoutError(f"Failed searching for subject or element is not found: {e}")

    def __input_from_name(self, name):
        try:
            name_input = self.page.locator(self.FROM_NAME_INPUT_SELECTOR)
            name_input.wait_for(state="visible", timeout=10000)
            name_input.fill(name)
            print(f"Successfully filled from name: {name}")
        except Exception as e:
            raise TimeoutError(f"Failed searching for name or element is not found: {e}")

    def edited_domain_conf(self):
        self.__input_subject_info(subject=Simulations.SIMULATION_TRAINING_EDITED_SUBJECT)
        self.__input_from_name(name=Simulations.SIMULATION_TRAINING_EDITED_NAME)
        time.sleep(3)
        self.__click_next_or_finish()

    def __search_for_edited_groups(self, group_name):
        try:
            # Find and click the search bar to activate it
            search_bar = self.page.locator(self.SEARCH_GROUP_INPUT)
            if search_bar.count() > 1:
                search_bar = search_bar.first
                print(f"Found {search_bar.count()} search inputs, using the first one")
            search_bar.wait_for(state="visible", timeout=20000)
            search_bar.click()
            # Clear any existing text and fill with the group name
            search_bar.fill("")
            search_bar.fill(group_name)
            # Wait for search results to load
            self.page.wait_for_timeout(2000)
            # Wait for the table to load (similar to the original wait_until_dcoya_table_loaded)
            # Try to find the group in the filtered results and click it
            group_locator = self.page.locator(self.GROUP_NAME_CHECKBOX)
            group_locator.wait_for(state="visible", timeout=10000)
            group_locator.click()
            print(f"Clicked on filtered group: {group_name}")
            # Click the Next or Finish button
            self.__click_next_or_finish()
            print("Clicked Next after selecting group")
        except Exception as e:
            raise Exception(f"FAILED Searching for: {self.GROUP_NAME_CHECKBOX} - {e}")

    def select_edited_audience(self):
        self.__search_for_edited_groups(group_name=GroupNames.EDITED_GROUP_NAME)

    def create_simulation(self):
        self.__click_next_or_finish()

    def edit_simulation(self):
        """Main method to edit a simulation - selects campaign and edits the most recent simulation"""
        try:
            print("Starting simulation edit process...")
            self.select_campaign_and_simulation()
            self.edited_domain_conf()
            self.select_edited_audience()
            self.create_simulation()
            print("Created simulation")
        except Exception as e:
            raise Exception(f"Simulation edit process failed: {e}")



