from playwright.sync_api import Page

from base.logger import QALogger
from data.campaigns import Campaigns
from data.group_names import GroupNames
from data.users_info import UsersInfo


class PhishingCampaign:
    SMART_BUTTON = "//button[normalize-space()='Smart']"
    GROUP_NAME = f"//div[@title='{GroupNames().Group_NAME}']"
    SELECT_ALL = "//span[normalize-space()='Select all']"
    SEARCH_BUTTON = "//div[@id='audience-search']//input"
    NEXT_BUTTON = "//button[normalize-space()='Next']"
    FINISH_BUTTON = "next_finish_button"
    CREATE_CAMPAIGN_NAME = Campaigns.CAMPAIGN_NAME_PHISHING
    SELECT_CAMPAIGN = f"//div[contains(@aria-label, '{CREATE_CAMPAIGN_NAME}')]"
    DELETE_ICON_ID = "camp-control-delete-button"
    APPROVE_DELETE = ("//*[contains(text(), 'Do you want to delete selected campaign?')]//following::button["
                      "@id='system-button_cooldown-false'][2]")

    def __init__(self, page: Page):
        self.page = page
        self.timeout = 30000  # Playwright uses milliseconds
        self.logger = QALogger("phishingLogger").get_logger()

    def __click_smart(self):
        try:
            smart_button = self.page.locator(self.SMART_BUTTON)
            smart_button.wait_for(state="visible", timeout=self.timeout)
            smart_button.click()
        except Exception:
            raise Exception("Smart button not found")

    def __click_search(self, text, group_name):
        group_xpath = self.GROUP_NAME.format(group_name=group_name)

        try:
            search_button = self.page.locator(self.SEARCH_BUTTON)
            search_button.wait_for(state="visible", timeout=self.timeout)
            search_button.click()

            search_input = self.page.locator(self.SEARCH_BUTTON)
            search_input.fill(text)

            # Wait for table to load
            table_loaded = self.page.locator("//*[@id='dcoya-table-audience_loaded']")
            table_loaded.wait_for(state="visible", timeout=self.timeout)

            group_element = self.page.locator(group_xpath)
            if group_element.count() > 0:
                group_element.click()
            else:
                print(f"Group '{group_name}' not found")
        except Exception:
            raise Exception("Search button not found")

    def __click_on_select_all(self):
        try:
            select_all_checkbox = self.page.locator(self.SELECT_ALL)
            select_all_checkbox.wait_for(state="visible", timeout=self.timeout)
            select_all_checkbox.click(force=True)
        except Exception:
            raise TimeoutError("select all checkbox not clickable")

    def __click_next(self):
        try:
            next_button = self.page.locator(self.NEXT_BUTTON)
            next_button.wait_for(state="visible", timeout=self.timeout)
            next_button.click()
        except Exception:
            raise Exception("Next button not found")

    def __click_approve_campaign_phishing(self):
        try:
            finish_button = self.page.locator(f"//button[@id='{self.FINISH_BUTTON}_cooldown-false']")
            finish_button.wait_for(state="visible", timeout=self.timeout)
            finish_button.click()
        except Exception:
            raise Exception("Finish button not clickable or not found")

    def __select_audience(self):
        self.__click_search(UsersInfo.first_name, UsersInfo.last_name)
        self.__click_next()

    def __select_phishing_template(self):
        self.__click_on_select_all()
        self.__click_next()

    def __click_finish_to_create_campaign(self):
        self.__click_approve_campaign_phishing()

    def __click_next_multiple_time(self, times=3):
        for _ in range(times):
            self.__click_next()
        self.__click_finish_to_create_campaign()

    def __select_campaign(self):
        try:
            campaign_element = self.page.locator(self.SELECT_CAMPAIGN)
            campaign_element.wait_for(state="visible", timeout=self.timeout)
            campaign_element.click()
        except Exception:
            raise Exception("Campaign not found")

    def __click_delete_icon(self):
        try:
            delete_icon = self.page.locator(f"//button[@id='{self.DELETE_ICON_ID}_cooldown-false']")
            delete_icon.wait_for(state="visible", timeout=self.timeout)
            delete_icon.click()
        except Exception:
            raise Exception("Delete icon not found")

    def __approve_delete(self):
        try:
            delete_button = self.page.locator(self.APPROVE_DELETE)
            delete_button.wait_for(state="visible", timeout=self.timeout)
            delete_button.click()
        except Exception:
            raise Exception("Delete button not found")

    def delete_campaign(self):
        try:
            self.__select_campaign()
        except Exception as e:
            print(f"Error selecting campaign, creating manually first: {e}")
            self.create_campaign_smart()
            self.__select_campaign()
        self.__click_delete_icon()
        self.__approve_delete()

    def create_campaign_smart(self):
        self.__click_smart()
        self.__select_audience()
        self.__select_phishing_template()
        self.__click_next_multiple_time()
        self.__click_finish_to_create_campaign()
