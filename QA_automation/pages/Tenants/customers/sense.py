import time

from selenium.webdriver.common.by import By
from base.logger import <PERSON><PERSON><PERSON>ger
from data.campaigns import Campaigns
from data.group_names import GroupNames
from data.users import Users
from data.users_info import UsersInfo
from pages.waiting_utility import WaitingUtility


class Sense:
    """
    Testing training sense:
    1. Selecting audience.
    2. Selecting templates.
    3. Selecting email template.
    4. Creating the new sense campaign.
    """

    SENSE_BUTTON = "//button[normalize-space()='SENSE Campaign']"
    SENSE_CUSTOM_BUTTON = "//*[contains(@class, 'fa-light') and contains(@class, 'fa-wand-magic-sparkles')]"
    NEXT_BUTTON = "//button[normalize-space()='Next']"
    SEARCH_BUTTON = "//div[@id='audience-search']//input"

    GROUP_NAME = f"//div[@title='{GroupNames().Group_NAME}']"

    UNSELECT_TEMPLATE_BUTTON = "//button[normalize-space()='Unselect']"
    SELECT_TEMPLATE_BUTTON = "//button[normalize-space()='Select']"
    CREATE_BUTTON = "//button[normalize-space()='Create']"
    EMAIL_DROPDOWN = "//div[@id='emailEnrollment_dcoya-drop-down-root']"
    CUSTOM_EMAIL = " //div[contains(text(),'Select custom template from the email gallery')]"
    TEMPLATE_ASANA = "//*[@id='templates-grid']/div[1]/div"
    SELECT_TEMPLATE = "(//button[@type='button'][normalize-space()='Select'])[1]"
    CREATE_CAMPAIGN_NAME = Campaigns.CAMPAIGN_NAME_SENSE
    SELECT_CAMPAIGN = f"//div[contains(@aria-label, '{CREATE_CAMPAIGN_NAME}')]"
    CLICK_ON_GREAT = "//button[normalize-space()='Great!']"
    DELETE_ICON_ID = "camp-control-delete-button"
    APPROVE_DELETE = ("//*[contains(text(), 'Do you want to delete selected campaign?')]//following::button["
                      "@id='system-button_cooldown-false'][2]")

    def __init__(self, driver):
        self.driver = driver
        self.waitingUtility = WaitingUtility(driver)
        self.logger = QALogger("SenseLogger").get_logger()

    def __click_sense(self):
        if self.waitingUtility.element_exists(By.XPATH, self.SENSE_BUTTON):
            sense_button = self.waitingUtility.wait_for_element(By.XPATH, self.SENSE_BUTTON)
            sense_button.click()
        else:
            raise Exception("Sense button not found")

    def __select_custom_sense(self):
        if self.waitingUtility.element_exists(By.XPATH, self.SENSE_CUSTOM_BUTTON):
            custom_button = self.waitingUtility.wait_for_element(By.XPATH, self.SENSE_CUSTOM_BUTTON)
            custom_button.click()
        else:
            raise Exception("Custom sense button not found")

    def __click_next(self):
        next_button = self.waitingUtility.wait_until_dcoya_system_button_not_in_cooldown("campaign-next-create-button")
        if next_button is not None:
            next_button.click()
        else:
            raise Exception("Next button not found")

    def __click_search(self, text, group_name):
        group_xpath = self.GROUP_NAME.format(group_name=group_name)

        if self.waitingUtility.element_exists(By.XPATH, self.SEARCH_BUTTON):
            search_button = self.waitingUtility.wait_for_element(By.XPATH, self.SEARCH_BUTTON)
            search_button.click()

            search_input = self.waitingUtility.wait_for_element(By.XPATH, self.SEARCH_BUTTON)
            search_input.send_keys(text)
            if not self.waitingUtility.wait_until_dcoya_table_loaded("dcoya-table-audience"):
                raise Exception("In sense, the audience table did not load.")

            if self.waitingUtility.element_exists(By.XPATH, group_xpath):
                group = self.waitingUtility.wait_for_element(By.XPATH, group_xpath)
                group.click()
            else:
                print(f"Group '{group_name}' not found")
        else:
            raise Exception("Search button not found")

    def __click_email_drop_down(self):
        click_dropdown_email = self.waitingUtility.wait_for_element(By.XPATH, self.EMAIL_DROPDOWN)
        if click_dropdown_email.is_displayed():
            click_dropdown_email.click()
        else:
            raise Exception("Email dropdown not found")

    def __select_custom_email(self):
        click_custom_email = self.waitingUtility.wait_for_element(By.XPATH, self.CUSTOM_EMAIL)
        if click_custom_email.is_displayed():
            click_custom_email.click()
        else:
            raise Exception("Custom email not found")

    def __select_template_asana(self):
        template_asana = self.waitingUtility.wait_for_element(By.XPATH, self.TEMPLATE_ASANA)
        if template_asana.is_displayed():
            template_asana.click()
        else:
            raise Exception("Template Asana element does not exist")

    def __click_select_on_template(self):
        select_button = self.waitingUtility.wait_for_element(By.XPATH, self.SELECT_TEMPLATE)
        if select_button.is_displayed():
            select_button.click()
        else:
            raise Exception("Select button not found")

    def __select_campaign(self):
        campaign_element = self.waitingUtility.wait_for_element(By.XPATH, self.SELECT_CAMPAIGN)
        if campaign_element is not None and campaign_element.is_displayed():
            campaign_element.click()
        else:
            raise Exception("Campaign not found")

    def __click_on_great(self):
        if self.waitingUtility.element_exists(By.XPATH, self.CLICK_ON_GREAT):
            great_button = self.waitingUtility.wait_for_element(By.XPATH, self.CLICK_ON_GREAT)
            great_button.click()
        else:
            print("Great button not found")

    def __click_delete_icon(self):
        delete_icon = self.waitingUtility.wait_until_dcoya_system_button_not_in_cooldown(self.DELETE_ICON_ID)
        if delete_icon is not None:
            delete_icon.click()
        else:
            raise Exception("Delete icon not found")

    def __approve_delete(self):
        if self.waitingUtility.element_exists(By.XPATH, self.APPROVE_DELETE):
            delete_button = self.waitingUtility.wait_for_element(By.XPATH, self.APPROVE_DELETE)
            delete_button.click()
        else:
            raise Exception("Delete button not found")

    def __write_into_campaign_name_field(self):
        self.waitingUtility.write_into_dcoya_input(self.CREATE_CAMPAIGN_NAME, "Campaign-Name")

    def __select_audience(self):
        self.__click_next()
        self.__click_search(UsersInfo.first_name, GroupNames.Group_NAME)
        self.__click_next()
        self.waitingUtility.wait_until_template_grid("teachable-template-grid", loading=False)
        self.__click_next()

    def __email_select(self):
        self.waitingUtility.wait_until_template_grid("email-template-grid", loading=False)
        self.__click_next()

    def __set_scheduling(self):
        self.__click_next()

    def __click_create(self):
        create_button = self.waitingUtility.wait_for_element(By.XPATH, self.CREATE_BUTTON)
        if create_button.is_displayed():
            create_button = self.waitingUtility.wait_until_dcoya_system_button_not_in_cooldown(
                "campaign-next-create-button")
            if create_button is not None:
                create_button.click()
        else:
            raise Exception("Create button not found")

    def delete_campaign(self):
        try:
            self.__select_campaign()
        except Exception as e:
            print(f"Error selecting campaign, creating manually first: {e}")
            self.create_campaign()
            self.__select_campaign()
        self.__click_delete_icon()
        self.__approve_delete()

    def create_campaign(self):
        self.__click_sense()
        self.__select_custom_sense()
        self.__select_audience()
        self.__email_select()
        self.__set_scheduling()
        self.__click_create()
