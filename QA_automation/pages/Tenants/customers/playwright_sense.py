import time

from playwright.sync_api import Page
from base.logger import <PERSON><PERSON>ogger
from data.campaigns import Campaigns
from data.group_names import GroupNames
from data.users import Users
from data.users_info import UsersInfo


class PlaywrightSense:
    """
    Testing training sense:
    1. Selecting audience.
    2. Selecting templates.
    3. Selecting email template.
    4. Creating the new sense campaign.
    """

    SENSE_BUTTON = "//button[normalize-space()='SENSE Campaign']"
    SENSE_CUSTOM_BUTTON = "//*[contains(@class, 'fa-light') and contains(@class, 'fa-wand-magic-sparkles')]"
    NEXT_BUTTON = "//button[normalize-space()='Next']"
    SEARCH_BUTTON = "//div[@id='audience-search']//input"
    EMPLOYEES_TAB = "//button[normalize-space()='Employees']"
    TEST_QA_EMAIL = "//div[contains(text(), '<EMAIL>')]"
    COMPLETION_MESSAGE = "//div[contains(text(), 'Campaign created successfully') or contains(text(), 'Success') or contains(text(), 'completed') or contains(text(), 'Created')]"

    GROUP_NAME = f"//div[@title='{GroupNames().Group_NAME}']"

    UNSELECT_TEMPLATE_BUTTON = "//button[normalize-space()='Unselect']"
    SELECT_TEMPLATE_BUTTON = "//button[normalize-space()='Select']"
    CREATE_BUTTON = "//button[normalize-space()='Create']"
    EMAIL_DROPDOWN = "//div[@id='emailEnrollment_dcoya-drop-down-root']"
    CUSTOM_EMAIL = " //div[contains(text(),'Select custom template from the email gallery')]"
    TEMPLATE_ASANA = "//*[@id='templates-grid']/div[1]/div"
    SELECT_TEMPLATE = "(//button[@type='button'][normalize-space()='Select'])[1]"
    CREATE_CAMPAIGN_NAME = Campaigns.CAMPAIGN_NAME_SENSE
    SELECT_CAMPAIGN = f"//div[contains(@aria-label, '{CREATE_CAMPAIGN_NAME}')]"
    CLICK_ON_GREAT = "//button[normalize-space()='Great!']"
    DELETE_ICON_ID = "camp-control-delete-button"
    APPROVE_DELETE = ("//*[contains(text(), 'Do you want to delete selected campaign?')]//following::button["
                      "@id='system-button_cooldown-false'][2]")

    def __init__(self, page: Page):
        self.page = page
        self.logger = QALogger("SenseLogger").get_logger()

    def __click_sense(self):
        try:
            sense_button = self.page.locator(self.SENSE_BUTTON)
            sense_button.wait_for(state="visible", timeout=30000)
            sense_button.click()
            print("✅ SENSE Campaign button clicked successfully")
        except Exception as e:
            raise Exception(f"SENSE Campaign button not found or not clickable: {e}")

    def __click_custom_sense(self):
        try:
            custom_button = self.page.locator(self.SENSE_CUSTOM_BUTTON)
            custom_button.wait_for(state="visible", timeout=30000)
            custom_button.click()
            print("✅ Custom SENSE button clicked successfully")
        except Exception as e:
            raise Exception(f"Custom SENSE button not found or not clickable: {e}")

    def __click_search(self, text, group_name):
        try:
            # First click on Employees tab
            employees_tab = self.page.locator(self.EMPLOYEES_TAB)
            employees_tab.wait_for(state="visible", timeout=30000)
            employees_tab.click()
            print("✅ Employees tab clicked successfully")

            # Then search for QA
            search_button = self.page.locator(self.SEARCH_BUTTON)
            search_button.wait_for(state="visible", timeout=30000)
            search_button.click()

            search_input = self.page.locator(self.SEARCH_BUTTON)
            search_input.fill("QA")
            print("✅ Searched for 'QA'")

            # Wait a bit for search results to appear
            self.page.wait_for_timeout(2000)
            print("⏳ Waiting for search results...")

            # <NAME_EMAIL>
            test_qa_element = self.page.locator(self.TEST_QA_EMAIL)
            test_qa_element.wait_for(state="visible", timeout=30000)
            test_qa_element.click()
            print("✅ <EMAIL> selected successfully")
        except Exception as e:
            raise Exception(f"Search functionality failed: {e}")

    def __click_next(self):
        try:
            next_button = self.page.locator(self.NEXT_BUTTON)
            next_button.wait_for(state="visible", timeout=30000)
            next_button.click()
            print("✅ Next button clicked successfully")

        except Exception as e:
            print(f"⚠️ Next button click failed: {e}")
            # Try alternative Next button selectors
            alternative_selectors = [
                "//button[contains(text(), 'Next')]",
                "//button[@type='button'][contains(., 'Next')]",
                "//button[contains(@class, 'next')]",
                "button:has-text('Next')",
                "[data-testid*='next']",
                "button[id*='next']"
            ]

            for selector in alternative_selectors:
                try:
                    alt_button = self.page.locator(selector)
                    alt_button.wait_for(state="visible", timeout=5000)
                    alt_button.click()
                    print(f"✅ Next button clicked using alternative selector: {selector}")
                    return
                except:
                    continue

            raise Exception(f"Next button not found with any selector: {e}")

    def __click_on_unselect_template(self):
        try:
            unselect_button = self.page.locator(self.UNSELECT_TEMPLATE_BUTTON)
            unselect_button.wait_for(state="visible", timeout=30000)
            unselect_button.click()
            print("✅ Unselect template button clicked successfully")
        except Exception as e:
            print(f"Unselect template button not found (this might be expected): {e}")

    def __click_on_select_template(self):
        try:
            select_button = self.page.locator(self.SELECT_TEMPLATE_BUTTON)
            select_button.wait_for(state="visible", timeout=30000)
            select_button.click()
            print("✅ Select template button clicked successfully")
        except Exception as e:
            raise Exception(f"Select template button not found: {e}")

    def __click_on_email_dropdown(self):
        try:
            email_dropdown = self.page.locator(self.EMAIL_DROPDOWN)
            email_dropdown.wait_for(state="visible", timeout=30000)
            email_dropdown.click()
            print("✅ Email dropdown clicked successfully")
        except Exception as e:
            raise Exception(f"Email dropdown not found: {e}")

    def __click_on_custom_email(self):
        try:
            custom_email = self.page.locator(self.CUSTOM_EMAIL)
            custom_email.wait_for(state="visible", timeout=30000)
            custom_email.click()
            print("✅ Custom email option selected successfully")
        except Exception as e:
            raise Exception(f"Custom email option not found: {e}")

    def __click_on_template_asana(self):
        try:
            template_asana = self.page.locator(self.TEMPLATE_ASANA)
            template_asana.wait_for(state="visible", timeout=30000)
            template_asana.click()
            print("✅ Template Asana clicked successfully")
        except Exception as e:
            raise Exception(f"Template Asana not found: {e}")

    def __click_on_select_template_final(self):
        try:
            select_template = self.page.locator(self.SELECT_TEMPLATE)
            select_template.wait_for(state="visible", timeout=30000)
            select_template.click()
            print("✅ Final select template button clicked successfully")
        except Exception as e:
            raise Exception(f"Final select template button not found: {e}")

    def __click_on_create(self):
        try:
            create_button = self.page.locator(self.CREATE_BUTTON)
            create_button.wait_for(state="visible", timeout=30000)
            create_button.click()
            print("✅ Create button clicked successfully")
        except Exception as e:
            raise Exception(f"Create button not found: {e}")

    def __click_on_great(self):
        try:
            great_button = self.page.locator(self.CLICK_ON_GREAT)
            great_button.wait_for(state="visible", timeout=30000)
            great_button.click()
            print("✅ Great button clicked successfully")
        except Exception as e:
            print(f"Great button not found (this might be expected): {e}")

    def __select_audience(self):
        self.__click_next()
        self.__click_search("QA", "<EMAIL>")  # Updated parameters
        self.__click_next()
        # Wait for template grid to load, then just click Next (no template selection needed)
        try:
            template_grid = self.page.locator("#teachable-template-grid_loaded")
            template_grid.wait_for(state="visible", timeout=30000)
            print("✅ Template grid loaded successfully")
        except Exception as e:
            print(f"Template grid loading timeout (continuing anyway): {e}")
        # Just click Next to proceed past templates selection
        print("⏭️ Clicking Next to proceed past templates selection...")
        self.__click_next()

    def __email_select(self):
        # Wait for email template grid to load
        try:
            email_template_grid = self.page.locator("#email-template-grid_loaded")
            email_template_grid.wait_for(state="visible", timeout=30000)
            print("✅ Email template grid loaded successfully")
        except Exception as e:
            print(f"Email template grid loading timeout (continuing anyway): {e}")
        self.__click_next()

    def __set_scheduling(self):
        self.__click_next()

    def __click_create(self):
        try:
            create_button = self.page.locator(self.CREATE_BUTTON)
            create_button.wait_for(state="visible", timeout=30000)
            create_button.click()
            print("✅ Create button clicked successfully")
        except Exception as e:
            raise Exception(f"Create button not found: {e}")

    def __select_training_template(self):
        self.__click_on_unselect_template()
        self.__click_on_select_template()
        self.__click_next()

    def __select_email_template(self):
        self.__click_on_email_dropdown()
        self.__click_on_custom_email()
        self.__click_on_template_asana()
        self.__click_on_select_template_final()
        self.__click_next()

    def __create_campaign(self):
        self.__click_on_create()
        self.__click_on_great()

    def __select_campaign(self):
        try:
            campaign_element = self.page.locator(self.SELECT_CAMPAIGN)
            campaign_element.wait_for(state="visible", timeout=30000)
            campaign_element.click()
            print("✅ Campaign selected successfully")
        except Exception as e:
            raise Exception(f"Campaign not found: {e}")

    def __click_delete_icon(self):
        try:
            delete_icon = self.page.locator(f"//button[@id='{self.DELETE_ICON_ID}_cooldown-false']")
            delete_icon.wait_for(state="visible", timeout=30000)
            delete_icon.click()
            print("✅ Delete icon clicked successfully")
        except Exception as e:
            raise Exception(f"Delete icon not found: {e}")

    def __approve_delete(self):
        try:
            delete_button = self.page.locator(self.APPROVE_DELETE)
            delete_button.wait_for(state="visible", timeout=30000)
            delete_button.click()
            print("✅ Delete approved successfully")
        except Exception as e:
            raise Exception(f"Delete approval button not found: {e}")

    def delete_campaign(self):
        try:
            print("🗑️ Attempting to select and delete campaign...")
            self.__select_campaign()
        except Exception as e:
            print(f"Error selecting campaign, creating manually first: {e}")
            self.create_campaign()
            self.__select_campaign()
        self.__click_delete_icon()
        self.__approve_delete()
        print("✅ Campaign deletion completed!")

    def create_campaign(self):
        print("🚀 Starting SENSE campaign creation...")
        self.__click_sense()
        self.__click_custom_sense()
        print("📋 Selecting audience...")
        self.__select_audience()
        print("📧 Selecting email template...")
        self.__email_select()
        print("⏭️ Now clicking Next exactly 5 times to complete the flow...")
        # Click Next exactly 5 times as requested - this is critical for completion
        for i in range(5):
            print(f"⏭️ Clicking Next {i+1}/5...")
            try:
                self.__click_next()
                # Small wait between clicks to ensure UI updates
                self.page.wait_for_timeout(1000)
            except Exception as e:
                print(f"⚠️ Error clicking Next {i+1}/5: {e}")
                # Continue trying the remaining clicks
                continue
        print("✅ Completed clicking Next 5 times - SENSE campaign creation flow finished!")

    def wait_for_completion_message(self):
        """
        Wait for the completion message popup to appear after campaign creation
        """
        try:
            print("⏳ Waiting for completion message popup...")
            completion_message = self.page.locator(self.COMPLETION_MESSAGE)
            completion_message.wait_for(state="visible", timeout=30000)
            print("✅ Completion message popup appeared successfully!")
            return True
        except Exception as e:
            print(f"⚠️ Completion message popup not found (this might be expected): {e}")
            # Try to wait for any popup or modal that might contain success message
            try:
                print("🔍 Looking for any success popup or modal...")
                # Look for common success indicators
                success_indicators = [
                    "//div[contains(@class, 'success')]",
                    "//div[contains(@class, 'modal')]//div[contains(text(), 'success')]",
                    "//div[contains(@class, 'popup')]",
                    "//div[contains(@class, 'notification')]",
                    "//div[contains(@class, 'alert-success')]",
                    "//div[contains(@class, 'toast')]"
                ]

                for indicator in success_indicators:
                    try:
                        element = self.page.locator(indicator)
                        element.wait_for(state="visible", timeout=5000)
                        print(f"✅ Found success indicator: {indicator}")
                        return True
                    except:
                        continue

                print("ℹ️ No specific completion popup found, but campaign creation likely completed")
                return False
            except Exception as e2:
                print(f"ℹ️ No completion popup detected: {e2}")
                return False
