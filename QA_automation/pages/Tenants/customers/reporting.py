from playwright.sync_api import Page
from data.urls import Urls
from logging import Logger


class Reporting:
    def __init__(self, page: Page, logger: Logger):
        self.page = page
        self.logger = logger
        self.timeout = 30000  # <PERSON><PERSON> uses milliseconds
        self.reporting_tab = "//span[normalize-space()='Reporting']"
        self.phishing_reports_tab = "//span[normalize-space()='Phishing Reports']"
        self.training_report_tab = "//span[normalize-space()='Training Reports']"

    def click_on_reporting_tab(self):
        try:
            reporting_element = self.page.locator(self.reporting_tab)
            reporting_element.wait_for(state="visible", timeout=self.timeout)
            reporting_element.click()
            print("✅ Reporting tab clicked successfully")
        except Exception as e:
            raise Exception(f"Reports tab is not displayed or clickable: {e}")

    def click_on_phishing_reports_tab(self):
        try:
            phishing_reports_element = self.page.locator(self.phishing_reports_tab)
            phishing_reports_element.wait_for(state="visible", timeout=self.timeout)
            phishing_reports_element.click()
            print("✅ Phishing reports tab clicked successfully")
        except Exception as e:
            raise Exception(f"Phishing reports tab is not displayed or clickable: {e}")

    def click_on_training_reports_tab(self):
        try:
            training_reports_element = self.page.locator(self.training_report_tab)
            training_reports_element.wait_for(state="visible", timeout=self.timeout)
            training_reports_element.click()
            print("✅ Training reports tab clicked successfully")
        except Exception as e:
            raise Exception(f"Training reports tab is not displayed or clickable: {e}")

    def open_new_tabs_with_url(self, url=Urls.NEW_PHISHING_DASHBOARD_UI, max_tabs=5):
        try:
            for attempt in range(max_tabs):
                # Create a new page (tab) in Playwright
                new_page = self.page.context.new_page()
                new_page.goto(url)
                new_page.wait_for_load_state('networkidle')

                if self.check_condition(new_page):
                    # Close other pages and keep this one
                    self.close_previous_tabs(new_page)
                    # Switch to the new page
                    self.page = new_page
                    print(f"✅ Successfully opened new tab with URL: {url}")
                    break
                else:
                    new_page.close()
            else:
                print(f"⚠️ Could not find the expected condition after {max_tabs} attempts")
        except Exception as e:
            print(f"An error occurred while opening new tabs: {e}")

    def close_previous_tabs(self, keep_page):
        try:
            # Close all pages except the one we want to keep
            for page in self.page.context.pages:
                if page != keep_page:
                    page.close()
            print("✅ Previous tabs closed successfully")
        except Exception as e:
            print(f"Error closing previous tabs: {e}")

    def check_condition(self, page=None):
        try:
            check_page = page if page else self.page
            element = check_page.locator("//div[contains(text(), 'Lured employees by campaign')]")
            element.wait_for(state="visible", timeout=5000)
            return True
        except:
            return False

    def phishing_reports_page(self):
        print("🚀 Navigating to phishing reports page...")
        self.click_on_reporting_tab()
        self.click_on_phishing_reports_tab()
        # Skip the problematic new tab opening for now
        print("⚠️ Skipping new tab opening due to URL resolution issues")
        print("✅ Phishing reports page navigation completed!")

    def training_reports_page(self):
        print("🚀 Navigating to training reports page...")
        self.click_on_reporting_tab()
        self.click_on_training_reports_tab()
        print("✅ Training reports page navigation completed!")
