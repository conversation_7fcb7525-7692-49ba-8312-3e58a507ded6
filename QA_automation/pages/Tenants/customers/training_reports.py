import time
from logging import Logger
from selenium.webdriver.support import expected_conditions as EC
from typing import <PERSON>ple

from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webdriver import WebDriver
from selenium.webdriver.support.ui import WebDriverWait

from data.group_names import GroupNames
from pages.waiting_utility import WaitingUtility


class TrainingReports:

    def __init__(self, driver: WebDriver, logger: Logger):
        self.logger = logger
        self.driver: WebDriver = driver
        self.wait: WebDriverWait = WebDriverWait(self.driver, 30)
        self.waiting_utility = WaitingUtility(driver)
        self.logger = logger

    open_menu_drop_down_button_click: Tuple[str, str] = (
        By.XPATH, "//button[@id='open_menu_iconButton']")
    create_button_click: Tuple[str, str] = (By.XPATH, "//button[@id='create_edit_cooldown-false']")
    next_button_click: Tuple[str, str] = (By.XPATH, "//button[@id='employee-learner-report-next-apply_cooldown-false']")
    search_group: Tuple[str, str] = (By.XPATH, '//*[@id="DcoyaUpgradedInput_search_bar"]//input')
    apply_button_click: Tuple[str, str] = (
        By.XPATH, "//button[@id='employee-learner-report-next-apply_cooldown-false']")

    done_button_click: Tuple[str, str] = (By.XPATH, "(//button[normalize-space()='Done'])")
    delete_report_button: Tuple[str, str] = (By.XPATH, "//button[@id='delete_cooldown-false']")
    approve_delete: Tuple[str, str] = (By.XPATH, "//*[contains(text(), 'Do you want to delete this "
                                                 "report?')]//following::button[@id='system-button_cooldown-false'][2]")

    def click_on_open_menu_arrow(self) -> None:
        report_click = self.waiting_utility.click_on_element_avoid_stale_element(
            *self.open_menu_drop_down_button_click)
        if not report_click:
            raise TimeoutError("arrow dropdown is not responsive button is not responsive")

    def click_on_create_button(self) -> None:
        create_report = self.waiting_utility.click_on_element_avoid_stale_element(
            *self.create_button_click)
        if not create_report:
            raise TimeoutError("Create button is not responsive")

    def click_on_next_button(self) -> None:
        next_click = self.waiting_utility.click_on_element_avoid_stale_element(
            *self.next_button_click)
        if not next_click:
            raise TimeoutError("Next button is not responsive")

    def select_of_tested_group(self):
        search_box = self.wait.until(EC.element_to_be_clickable(self.search_group))
        if search_box.is_displayed():
            search_box.click()
            search_box.send_keys(GroupNames().Group_NAME)
        else:
            print("Element is not clickable")

    def click_on_apply_button(self) -> None:
        apply_click = self.wait.until(EC.element_to_be_clickable(self.apply_button_click))
        if apply_click.is_displayed():
            apply_click.click()
        else:
            print("Element is not clickable")

    def click_on_done_button(self) -> None:
        done_click = self.waiting_utility.click_on_element_avoid_stale_element(
            *self.done_button_click)
        if not done_click:
            raise TimeoutError("Done button is not responsive")

    def click_on_delete_campaign_button(self) -> None:
        delete_click = self.waiting_utility.click_on_element_avoid_stale_element(*self.delete_report_button)
        if not delete_click:
            raise TimeoutError("Delete  button is not responsive")

    def click_approve_delete(self) -> None:
        approve_delete_click = self.waiting_utility.click_on_element_avoid_stale_element(*self.approve_delete)
        if not approve_delete_click:
            raise TimeoutError("Approve delete button is not responsive")

    def select_audience(self):
        self.select_of_tested_group()
        self.click_on_next_button()
        self.click_on_apply_button()
        self.click_on_done_button()

    def create_new_learner_report(self) -> None:
        self.click_on_open_menu_arrow()
        self.click_on_create_button()
        self.click_on_next_button()
        self.select_audience()

    def delete_report(self) -> None:
        self.click_on_open_menu_arrow()
        self.click_on_delete_campaign_button()
        self.click_approve_delete()

    def delete_learner_report(self) -> None:
        self.delete_report()
