import time
from typing import <PERSON><PERSON>

from playwright.sync_api import Page

from base.logger import <PERSON><PERSON>ogger
from data.campaigns import Campaigns
from data.group_names import GroupNames
from data.simulations import Simulations

"""
Create new training simulation:
In this test I will create new training 
simulation under training campaign.
It will test the creation of the simulation
using the "New" tab inside the tested campaign.

"""


class SimulationTraining:
    NEW_BUTTON = "newSimulationButton"
    NEXT_BUTTON = "//button[normalize-space()='Next']"
    FROM_SUBJECT = "subjectInput"
    FROM_EMAIL_INPUT = "fromEmailInput"
    FROM_NAME_INPUT = "sentFromInput"
    SUBJECT_INPUT_SELECTOR = "#DcoyaUpgradedInput_subjectInput input"
    FROM_EMAIL_INPUT_SELECTOR = "#DcoyaUpgradedInput_fromEmailInput input"
    FROM_NAME_INPUT_SELECTOR = "#DcoyaUpgradedInput_sentFromInput input"
    DOMAINS_DROP_POPUP = f"//div[@id='domains-dropdown_{Simulations().SIMULATION_DROP_DOWN_NAME}']"
    SELECT_ALL_CHECKBOX = "(//div[normalize-space()='Select all'])[1]"
    DOMAINS_DROP_DOMAIN_SELECT = f"(//div[normalize-space()='{Simulations().SIMULATION_DOMAIN_NAME}'])[1]"
    SEARCH_GROUP_INPUT = "//div[@id='DcoyaUpgradedInput_search_bar']//input[1]"
    GROUP_NAME_CHECKBOX = f"//div[@title='{GroupNames().Group_NAME}']"
    FINISH_BUTTON = "(//button[normalize-space()='Finish'])"
    CREATE_CAMPAIGN_NAME = Campaigns.CAMPAIGN_NAME_SENSE
    SELECT_CAMPAIGN = f"//div[contains(@aria-label, '{CREATE_CAMPAIGN_NAME}')]"
    SELECT_TRAINING_TEMPLATE = "(//button[@id='button_select_toggle_cooldown-false'])[1]"
    SELECT_TRAINING_MESSAGE = "(//button[@id='button_select_toggle_cooldown-false'])[1]"
    SELECT_MESSAGE_TEMPLATE = "(//button[@id='button_select_toggle_cooldown-false'])[1]"
    SEARCH_TEMPLATES_BAR = '//div[@id="DcoyaUpgradedInput_training-filter"]//input'
    SEARCH_TEMPLATES_BAR_MESSAGE = '//div[@id="DcoyaUpgradedInput_search_bar"]//input'
    SEARCH_TEMPLATE_TRAINING_KEYWORD = 'NINJIO S07E01A Bad Robot_Hebrew'
    SEARCH_TEMPLATE_TRAINING_MESSAGE_KEYWORD = 'Asana - Complete your Sign Up [Eng]'
    TRAINING_TEMPLATE = "(//div[@id='card_container_NINJIO S07E01A Bad Robot_Hebrew'])[1]"
    TRAINING_TEMPLATE_MESSAGE = "//div[@id='card_container_Asana - Complete your Sign Up [Eng]']"

    def __init__(self, page: Page):
        self.page = page
        self.logger = QALogger("SimulationTrainingLogger").get_logger()

    def __create_new_button(self):
        try:
            new_button = self.page.locator(f"//button[@id='{self.NEW_BUTTON}_cooldown-false']")
            new_button.wait_for(state="visible", timeout=20)
            new_button.click()
            print("Successfully clicked new button")
        except Exception as e:
            raise Exception(f"Click new button is not clickable: {e}")

    def __click_next(self):
        try:
            next_button = self.page.locator(self.NEXT_BUTTON)
            next_button.wait_for(state="visible", timeout=20000)
            next_button.click()
            print("Successfully clicked Next button")
        except Exception as e:
            raise TimeoutError(f"Next button is not clickable: {e}")

    def __input_subject_info(self, subject):
        try:
            # Try the full selector first
            subject_input = self.page.locator(self.SUBJECT_INPUT_SELECTOR)
            subject_input.wait_for(state="visible", timeout=5000)
            subject_input.fill(subject)
            print(f"Successfully filled subject: {subject}")
        except Exception as e1:
            try:
                # Fallback to the training-specific selector
                subject_input = self.page.locator(f"#DcoyaUpgradedInput_{self.FROM_SUBJECT} input")
                subject_input.wait_for(state="visible", timeout=10000)
                subject_input.fill(subject)
                print(f"Successfully filled subject (fallback): {subject}")
            except Exception as e2:
                raise TimeoutError(f"Failed searching for subject or element is not found. Primary error: {e1}, Fallback error: {e2}")

    def __search_template_training(self):
        try:
            search_bar = self.page.locator(self.SEARCH_TEMPLATES_BAR)
            search_bar.wait_for(state="visible", timeout=10000)
            search_bar.click()
            search_bar.fill(self.SEARCH_TEMPLATE_TRAINING_KEYWORD)
            print(f"Successfully searched for template: {self.SEARCH_TEMPLATE_TRAINING_KEYWORD}")
        except Exception as e:
            raise Exception(f"Failed to search template: {e}")

    def __search_template_message(self, searched_text):
        try:
            search_bar = self.page.locator(self.SEARCH_TEMPLATES_BAR_MESSAGE)
            search_bar.wait_for(state="visible", timeout=10000)
            search_bar.fill("")
            search_bar.fill(searched_text)
            self.page.wait_for_timeout(2000)
            print(f"Successfully searched for template with text: '{searched_text}'")
            return True
        except Exception as e:
            print(f"Failed to search for template with text: '{searched_text}': {e}")
            return False

    def __hover_selected_template(self, template, message):
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                template_element = self.page.locator(template)
                template_element.wait_for(state="visible", timeout=10000)
                template_element.hover()
                
                select_button = self.page.locator(message)
                select_button.wait_for(state="visible", timeout=5000)
                select_button.click()
                
                print("Successfully hovered and selected the template.")
                return True
            except Exception as e:
                print(f"Failed to hover and select template (attempt {attempt + 1}/{max_attempts}): {e}")
                if attempt == max_attempts - 1:
                    raise Exception("Failed to hover and select template after maximum attempts")
        return False

    def __hover_selected_template_training(self):
        self.__hover_selected_template(self.TRAINING_TEMPLATE, self.SELECT_TRAINING_MESSAGE)

    def __click_on_select_message_button(self):
        try:
            select_training_message_template_button = self.page.locator(self.TRAINING_TEMPLATE_MESSAGE).first
            select_training_message_template_button.wait_for(state="visible", timeout=10000)
            select_training_message_template_button.click()

            select_button = self.page.locator(self.SELECT_MESSAGE_TEMPLATE).first
            select_button.wait_for(state="visible", timeout=10000)
            select_button.click()
            print("Successfully clicked on the Select Message button.")
        except Exception as e:
            print(f"Failed to click on the Select Message button: {e}")

    def __input_from_email(self, email):
        try:
            email_input = self.page.locator(self.FROM_EMAIL_INPUT_SELECTOR)
            email_input.wait_for(state="visible", timeout=10000)
            email_input.fill(email)
            print(f"Successfully filled from email: {email}")
        except Exception as e:
            raise TimeoutError(f"Failed searching for email or element is not found: {e}")

    def __input_from_name(self, name):
        try:
            name_input = self.page.locator(self.FROM_NAME_INPUT_SELECTOR)
            name_input.wait_for(state="visible", timeout=10000)
            name_input.fill(name)
            print(f"Successfully filled from name: {name}")
        except Exception as e:
            raise TimeoutError(f"Failed searching for name or element is not found: {e}")

    def __click_domains_drop_down(self):
        try:
            domains_dropdown = self.page.locator(self.DOMAINS_DROP_POPUP)
            domains_dropdown.wait_for(state="visible", timeout=20000)
            domains_dropdown.click()
            print("Successfully clicked domains dropdown")
        except Exception as e:
            raise TimeoutError(f"Failed to open domains dropdown: {e}")

    def __click_select_all(self):
        try:
            select_all = self.page.locator(self.SELECT_ALL_CHECKBOX)
            select_all.wait_for(state="visible", timeout=20000)
            select_all.click()
            print("Successfully clicked select all checkbox")
        except Exception as e:
            raise TimeoutError(f"Failed to click select all checkbox: {e}")

    def __click_on_select_button(self):
        try:
            select_button = self.page.locator(self.SELECT_TRAINING_TEMPLATE)
            select_button.wait_for(state="visible", timeout=20000)
            select_button.click()
            print("Successfully clicked select button")
        except Exception as e:
            raise TimeoutError(f"Failed to click select button: {e}")

    def clear_domain_list(self):
        self.__click_select_all()

    def __selected_domain(self):
        try:
            domain_select = self.page.locator(self.DOMAINS_DROP_DOMAIN_SELECT)
            domain_select.wait_for(state="visible", timeout=20000)
            domain_select.click()
            print("Successfully selected domain")
        except Exception as e:
            raise TimeoutError(f"Failed to select the domain checkbox: {e}")

    def __exit_domains_drop_down(self):
        try:
            self.page.keyboard.press('Escape')
            print("Successfully exited domains dropdown")
        except Exception as e:
            print(f"Error while sending ESC key: {e}")

    def __search_for_groups(self, group_name):
        try:
            search_bar = self.page.locator(self.SEARCH_GROUP_INPUT)
            if search_bar.count() > 1:
                search_bar = search_bar.first
                print(f"Found {search_bar.count()} search inputs, using the first one")
            search_bar.wait_for(state="visible", timeout=20000)
            search_bar.click()
            search_bar.fill("")
            search_bar.fill(group_name)
            self.page.wait_for_timeout(2000)
            
            group_checkbox = self.page.locator(self.GROUP_NAME_CHECKBOX)
            group_checkbox.wait_for(state="visible", timeout=10000)
            group_checkbox.click()
            print(f"Successfully selected group: {group_name}")
        except Exception as e:
            raise Exception(f"FAILED Searching for: {self.GROUP_NAME_CHECKBOX} - {e}")

    def __click_in_search_group_checkbox(self):
        try:
            group_checkbox = self.page.locator(self.GROUP_NAME_CHECKBOX)
            group_checkbox.wait_for(state="visible", timeout=20000)
            group_checkbox.click()
            print("Successfully clicked group checkbox")
        except Exception as e:
            raise TimeoutError(f"Checkbox of the search group is not clickable or element is not reachable: {e}")

    def __click_finish(self):
        try:
            finish_button = self.page.locator(self.FINISH_BUTTON)
            finish_button.wait_for(state="visible", timeout=20000)
            finish_button.click()
            print("Successfully clicked Finish button")
        except Exception as e:
            raise TimeoutError(f"Finish button is not responsive: {e}")

    def click_new_button(self):
        self.__create_new_button()

    def domain_conf(self):
        self.__input_subject_info(subject=Simulations.SIMULATION_TRAINING_SUBJECT)
        self.__input_from_email(email=Simulations.SIMULATION_DOMAIN_NAME)
        self.__input_from_name(name=Simulations.SIMULATION_TRAINING_NAME)
        self.__click_domains_drop_down()
        self.__click_select_all()
        self.clear_domain_list()
        self.__selected_domain()
        time.sleep(0.5)
        self.__exit_domains_drop_down()
        self.__click_next()

    def select_audience(self):
        self.__search_for_groups(group_name=GroupNames.Group_NAME)
        self.__click_next()

    def select_template_training(self):
        self.__search_template_training()
        self.__hover_selected_template_training()

    def select_template_message_training(self):
        self.__click_on_select_message_button()

    def create_simulation(self):
        self.__click_finish()

    def campaign_for_tested_simulation(self):
        try:
            self.select_campaign_and_simulation()
        except Exception as e:
            print(f"Error selecting campaign, creating manually first: {e}")

    def create_new_simulation_training(self):
        self.click_new_button()
        self.select_template_training()
        self.select_template_message_training()
        self.domain_conf()
        self.select_audience()
        self.create_simulation()

    def select_campaign_and_simulation(self):
        """Select the campaign and prepare for editing (Playwright style, like simulation_phishing)"""
        try:
            campaign_element = self.page.locator(self.SELECT_CAMPAIGN).first
            campaign_element.wait_for(state="visible", timeout=10000)
            if campaign_element.is_visible():
                campaign_element.click()
                print(f"Successfully selected campaign: {self.CREATE_CAMPAIGN_NAME}")
            else:
                raise Exception("Campaign not found")
        except Exception as e:
            raise Exception(f"Campaign not found: {e}")

        # Select the simulation that was created in the previous test
        try:
            # Wait for the page to load after campaign selection
            self.page.wait_for_timeout(2000)
            
            # Look for the "New Simulation created on" aria-label with retry mechanism
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    simulation_element = self.page.locator('[aria-label*="New Simulation created on"]').first
                    simulation_element.wait_for(state="visible", timeout=5000)
                    
                    # Check if element is still attached to DOM
                    if simulation_element.is_visible():
                        simulation_element.click()
                        print("Successfully selected the created simulation")
                        self.page.wait_for_timeout(1000)
                        return
                    else:
                        print(f"Simulation element not visible on attempt {attempt + 1}")
                        
                except Exception as e:
                    print(f"Attempt {attempt + 1} failed: {e}")
                    if attempt < max_attempts - 1:
                        self.page.wait_for_timeout(1000)
                        continue
                    else:
                        raise Exception(f"Failed to select simulation after {max_attempts} attempts: {e}")
                        
        except Exception as e:
            print(f"Warning: Could not select simulation, continuing without it: {e}")
