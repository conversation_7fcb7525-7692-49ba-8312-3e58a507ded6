from selenium.webdriver.common.by import By

from pages.waiting_utility import WaitingUtility


class StatusWindowTraining:
    SIMULATION_CHECK_BOX_SELECT = "//input[@aria-label='Select row']"
    START_BUTTON = "(//i[@class='fa-light fa-play'])[2]"
    STATUS_BUTTON = "//i[@class='fa-light fa-chart-column']"
    EMPLOYEE_CHECKBOX = "//input[@aria-label='Select row']"
    COMPLETED_STATUS = "need to complete"

    def __init__(self, driver):
        self.driver = driver
        self.waitingUtility = WaitingUtility(driver)

    def __select_simulation_checkbox(self):
        group_checkbox = self.waitingUtility.click_on_element_avoid_stale_element(By.XPATH,
                                                                                  self.SIMULATION_CHECK_BOX_SELECT)
        if not group_checkbox:
            raise TimeoutError("Checkbox of tested simulation is not clickable or not found")

    def __start_button_click(self):
        group_checkbox = self.waitingUtility.click_on_element_avoid_stale_element(By.XPATH,
                                                                                  self.START_BUTTON)
        if not group_checkbox:
            raise TimeoutError("Start button is not clickable or not found")

    def __click_on_status_button(self):
        group_checkbox = self.waitingUtility.click_on_element_avoid_stale_element(By.XPATH, self.STATUS_BUTTON)
        if not group_checkbox:
            raise TimeoutError("Status button is not clickable or not found")

    def __select_employee_checkbox(self):
        group_checkbox = self.waitingUtility.click_on_element_avoid_stale_element(By.XPATH, self.EMPLOYEE_CHECKBOX)
        if not group_checkbox:
            raise TimeoutError("Employee checkbox is not clickable or not found")

    def complete_training_tested_employee(self):
        self.__select_simulation_checkbox()
        self.__start_button_click()
        self.__click_on_status_button()
        self.__select_employee_checkbox()
