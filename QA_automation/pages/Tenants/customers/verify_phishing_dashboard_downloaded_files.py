import os


class VerifyPhishingDashboardDownloadedFiles:
    @staticmethod
    def check_file(file_path, expected_name, expected_extension, min_size, max_size):

        if not os.path.exists(file_path):
            return False, "File does not exist."

        file_size = os.path.getsize(file_path)

        file_name, file_extension = os.path.splitext(os.path.basename(file_path))

        if file_name != expected_name:
            return False, "File name does not match."

        if file_extension != expected_extension:
            return False, "File extension does not match."

        if file_size < min_size or file_size > max_size:
            return False, "File size is out of the expected range."

        return True, "File is valid."


def main():
    expected_name = "program-progress(1)"
    expected_extension = ".xlsx"
    relative_path = f"QA_automation\\tests\\tenants\\customer\\reporting\\phishing_report_files\\{expected_name}{expected_extension}"
    file_path = os.path.abspath(relative_path)
    min_size = 1024
    max_size = 1048576

    is_valid, message = VerifyPhishingDashboardDownloadedFiles.check_file(
        file_path, expected_name, expected_extension, min_size, max_size)
    print(message)


if __name__ == "__main__":
    main()
