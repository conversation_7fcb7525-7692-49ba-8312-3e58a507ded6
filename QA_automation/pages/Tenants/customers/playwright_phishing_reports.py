from playwright.sync_api import Page

class PlaywrightPhishingReports:
    def __init__(self, page: Page):
        self.page = page
        self.timeout = 30000  # milliseconds

        # Locators
        self.select_campaign_dropdown = "//div[@id='SelectCampaignDropdown_dcoya-drop-down-input']"
        self.select_simulation_dropdown = "//div[@id='SelectSimulationsDropdown_dcoya-drop-down-input']"
        self.select_branches_dropdown = "//button[@id='SelectBranchesDropdown_toggleDropdownButton_iconButton']"
        self.select_departments_dropdown = "//button[@id='SelectDepartmentsDropdown_toggleDropdownButton_iconButton']"
        self.select_all_checkbox = "//div[@title='Select all']//input[@type='checkbox']"
        self.select_all_div = "//div[contains(text(),'Select all')]"
        self.clear_dropdown = "//button[@id='SelectCampaignDropdown_clearDropdownButton_iconButton']"
        self.click_apply_button = "//button[@id='apply-button_cooldown-false']"
        self.click_reset_button = "//button[@id='system-button_cooldown-false']"
        self.click_export_button = "//*[@id='system-button_cooldown-false']/p[contains(text(), 'Reset')]"

    def open_dropdown(self, locator: str, name: str):
        print(f"Opening {name} dropdown...")
        dropdown = self.page.locator(locator)
        dropdown.wait_for(state="visible", timeout=self.timeout)
        dropdown.click()
        self.page.wait_for_timeout(500)

    def click_select_all_checkbox(self, name: str):
        print(f"Trying to click 'Select all' checkbox in {name} dropdown...")
        checkbox = self.page.locator(self.select_all_checkbox)
        if checkbox.count() > 0 and checkbox.first.is_visible():
            checkbox.first.click(force=True)
            print(f"Clicked 'Select all' checkbox in {name} dropdown.")
            self.page.wait_for_timeout(500)
            return True
        print(f"'Select all' checkbox not found in {name} dropdown.")
        return False

    def click_select_all_div(self, name: str):
        print(f"Trying to click 'Select all' div in {name} dropdown...")
        select_all = self.page.locator(self.select_all_div)
        if select_all.count() > 0 and select_all.first.is_visible():
            select_all.first.click(force=True)
            print(f"Clicked 'Select all' div in {name} dropdown.")
            self.page.wait_for_timeout(500)
            return True
        print(f"'Select all' div not found in {name} dropdown.")
        return False

    def clear_campaign_selections(self):
        print("Clearing campaign selections...")
        try:
            # Wait for the dropdown to be stable before trying to clear
            self.page.wait_for_load_state('networkidle', timeout=5000)

            clear_btn = self.page.locator(self.clear_dropdown)
            clear_btn.wait_for(state="visible", timeout=self.timeout)

            # Check if button is still attached and visible
            if clear_btn.is_visible():
                # Use force click to bypass potential overlapping elements
                clear_btn.click(force=True)
                print("✅ Campaign selections cleared successfully")
                self.page.wait_for_timeout(1000)  # Longer wait for UI to update
            else:
                print("⚠️ Clear button not visible, skipping clear operation")

        except Exception as e:
            print(f"⚠️ Could not clear campaign selections: {e}")
            # Continue without clearing - this is not critical for the test

    def click_apply(self):
        print("Clicking Apply button...")
        btn = self.page.locator(self.click_apply_button)
        btn.wait_for(state="visible", timeout=self.timeout)
        btn.click()
        self.page.wait_for_timeout(500)

    def click_reset(self):
        print("Clicking Reset button...")
        btn = self.page.locator(self.click_reset_button)
        btn.wait_for(state="visible", timeout=self.timeout)
        btn.click()
        self.page.wait_for_timeout(500)

    def click_export(self):
        print("Clicking Export button...")
        btn = self.page.locator(self.click_export_button)
        btn.wait_for(state="visible", timeout=self.timeout)
        btn.click()
        self.page.wait_for_timeout(500)

    def phishing_dropdowns(self):
        # Campaign dropdown
        self.open_dropdown(self.select_campaign_dropdown, "Campaign")
        self.clear_campaign_selections()
        self.click_select_all_checkbox("Campaign") or self.click_select_all_div("Campaign")
        self.open_dropdown(self.select_campaign_dropdown, "Campaign")  # Close
        self.page.wait_for_timeout(500)

        # Simulation dropdown
        self.open_dropdown(self.select_simulation_dropdown, "Simulation")
        self.click_select_all_checkbox("Simulation") or self.click_select_all_div("Simulation")
        self.open_dropdown(self.select_simulation_dropdown, "Simulation")  # Close
        self.page.wait_for_timeout(500)

        # Branches dropdown
        self.open_dropdown(self.select_branches_dropdown, "Branches")
        self.click_select_all_checkbox("Branches") or self.click_select_all_div("Branches")
        self.open_dropdown(self.select_branches_dropdown, "Branches")  # Close
        self.page.wait_for_timeout(500)

        # Departments dropdown
        self.open_dropdown(self.select_departments_dropdown, "Departments")
        self.click_select_all_checkbox("Departments") or self.click_select_all_div("Departments")
        self.open_dropdown(self.select_departments_dropdown, "Departments")  # Close
        self.page.wait_for_timeout(500)

        # Apply
        self.click_apply()