from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait

from data.campaigns import Campaigns


class TrainingCampaigns:
    def __init__(self, driver):
        self.driver = driver
        self.wait = WebDriverWait(self.driver, 20)
        self.sense_tab = (By.XPATH, "click on sense- //button[normalize-space()='SENSE']")
        self.campaign_name = (
            By.XPATH, f"//div[@class='sc-cezyBN fnqngm'][contains(text(), '{Campaigns.CAMPAIGN_NAME_SENSE}')]")

    def click_on_sense_tab(self):
        sense_element = self.wait.until(EC.element_to_be_clickable(self.sense_tab))
        if sense_element.is_displayed():
            sense_element.click()
        else:
            raise Exception("sense tab is not clickable")

    def click_on_campaign_name(self):
        self.wait.until(EC.element_to_be_clickable(self.campaign_name)).click()
