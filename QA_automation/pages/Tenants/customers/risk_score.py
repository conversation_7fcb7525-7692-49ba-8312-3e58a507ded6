
from data.urls import Urls


class RiskScore:

    def __init__(self, driver):
        self.driver = driver

    def browse_to_phishing_simulation_url_link(self):
        phishing_simulation_url = Urls.RISK_SCORE_PHISHING_SIMULATION
        self.driver.get(phishing_simulation_url)
        print(f"Opened URL: {phishing_simulation_url}")
        self.driver.refresh()
        print("Page refreshed successfully.")

    def browse_to_training_simulation_url_link(self):
        training_simulation_url = Urls.RISK_SCORE_TRAINING_SIMULATION
        self.driver.get(training_simulation_url)
        print(f"Opened URL: {training_simulation_url}")
        self.driver.refresh()
        print("Page refreshed successfully.")

