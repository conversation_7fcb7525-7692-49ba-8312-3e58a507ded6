from selenium.common import TimeoutException, NoSuchElementException, ElementClickInterceptedException
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from typing import Tuple
from selenium.webdriver.remote.webdriver import WebDriver

from pages.waiting_utility import WaitingUtility


class PhishingReports:
    def __init__(self, driver: WebDriver):
        self.driver: WebDriver = driver
        self.wait: WebDriverWait = WebDriverWait(self.driver, 300)
        self.waiting_utility = WaitingUtility(driver)

        self.select_campaign_dropdown: Tuple[str, str] = (
            By.XPATH, "//div[@id='SelectCampaignDropdown_dcoya-drop-down-input']")
        self.select_all_campaigns: Tuple[str, str] = (By.XPATH, "//div[contains(text(),'Select all')]")
        self.select_simulation_dropdown: Tuple[str, str] = (
            By.XPATH, "//div[@id='SelectSimulationsDropdown_dcoya-drop-down-input']")
        self.select_all_simulations: Tuple[str, str] = (By.XPATH, "//div[contains(text(),'Select all')]")
        self.clear_dropdown: Tuple[str, str] = (
            By.XPATH, "//button[@id='SelectCampaignDropdown_clearDropdownButton_iconButton']")
        self.select_all_branches: Tuple[str, str] = (By.XPATH, "//button["
                                                               "@id='SelectBranchesDropdown_toggleDropdownButton_iconButton']")
        self.select_all_departments: Tuple[str, str] = (By.XPATH, "//button["
                                                                  "@id"
                                                                  "='SelectDepartmentsDropdown_toggleDropdownButton_iconButton']")
        self.click_apply_button: Tuple[str, str] = (By.XPATH, "//button[@id='apply-button_cooldown-false']")
        self.click_reset_button: Tuple[str, str] = (By.XPATH, "//button[@id='system-button_cooldown-false']")
        self.click_export_button: Tuple[str, str] = (By.XPATH, "//*[@id='system-button_cooldown-false']/p[contains("
                                                               "text(), 'Reset')]")

    def select_campaign(self) -> None:
        self.click_element(self.select_campaign_dropdown, "select campaign dropdown")

    def click_on_select_all(self) -> None:
        self.click_element(self.select_all_campaigns, "click 'Select all' for campaigns")

    def un_select_all_campaigns(self) -> None:
        self.click_element(self.clear_dropdown, "unselect all campaigns")

    def select_simulation(self) -> None:
        self.click_element(self.select_simulation_dropdown, "select simulation dropdown")

    def click_on_select_all_simulations(self) -> None:
        self.click_element(self.select_all_simulations, "click 'Select all' for simulations")

    def click_select_all_branches(self) -> None:
        self.click_element(self.select_all_branches, "select all branches")

    def click_select_all_departments(self) -> None:
        self.click_element(self.select_all_departments, "select all departments")

    def click_on_apply_button(self) -> None:
        self.click_element(self.click_apply_button, "click 'Apply' button")

    def click_on_reset_button(self) -> None:
        self.click_element(self.click_reset_button, "click 'Reset' button")

    def click_on_export_button(self) -> None:
        self.click_element(self.click_export_button, "click 'Export' button")

    # Helping method for clicking an element
    def click_element(self, element: Tuple[str, str], action_description: str) -> None:
        try:
            element_clicked = self.waiting_utility.click_on_element_avoid_stale_element(*element)
            if not element_clicked:
                raise TimeoutError(f"Failed to {action_description}")
        except (TimeoutException, NoSuchElementException, ElementClickInterceptedException) as e:
            raise Exception(f"Failed to {action_description}") from e

    def phishing_dropdowns(self) -> None:
        # self.un_select_all_campaigns()
        self.select_campaign()
        self.click_on_select_all()
        self.select_campaign()
        self.select_simulation()
        self.click_on_select_all()
        self.select_simulation()
        self.click_select_all_branches()
        self.click_on_select_all()
        self.click_select_all_branches()
        self.click_select_all_departments()
        self.click_on_select_all()
        self.click_select_all_departments()
        self.click_on_apply_button()
