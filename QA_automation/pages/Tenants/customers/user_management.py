from logging import Logger
from playwright.sync_api import Page

class UserManagement:
    def __init__(self, page: Page, logger: Logger):
        self.page = page
        self.logger = logger
        self.user_management_tab = "//span[normalize-space()='User Management']"
        self.users_and_groups_tab = "//span[normalize-space()='Users & Groups']"

    def click_on_user_management_tab(self):
        tab = self.page.locator(self.user_management_tab)
        tab.wait_for(state="visible", timeout=10000)
        tab.click()

    def click_on_users_and_groups_tab(self):
        tab = self.page.locator(self.users_and_groups_tab)
        tab.wait_for(state="visible", timeout=10000)
        tab.click()

    def users_and_group_page(self):
        self.click_on_user_management_tab()
        self.click_on_users_and_groups_tab()
