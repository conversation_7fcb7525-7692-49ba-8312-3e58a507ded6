from playwright.sync_api import Page

class Login:
    def __init__(self, page: Page):
        self.page = page
        self.username_selector = "#id_auth-username"
        self.password_selector = "#id_auth-password"
        self.login_button_selector = "//button[normalize-space()='Login']"

    def set_username(self, username):
        self.page.locator(self.username_selector).wait_for(state="visible", timeout=10000)
        self.page.fill(self.username_selector, username)

    def set_password(self, password):
        self.page.locator(self.password_selector).wait_for(state="visible", timeout=10000)
        self.page.fill(self.password_selector, password)

    def click_login(self):
        self.page.locator(self.login_button_selector).wait_for(state="visible", timeout=10000)
        self.page.locator(self.login_button_selector).click()

    def login_to_system(self, username, password):
        self.set_username(username)
        self.set_password(password)
        self.click_login()
