from playwright.sync_api import Page


class MultiTenantPropertiesPage:
    def __init__(self, page: Page):
        self.page = page

    def choose_customer(self):
        for attempt in range(4):
            try:
                locator = self.page.locator("//div[normalize-space(.)='dcoya']")
                locator.first.wait_for(state="visible", timeout=10000)
                count = locator.count()
                print(f"Found {count} elements for customer selection, clicking the first one.")
                locator.first.click()
                break
            except Exception as e:
                if attempt < 3:
                    print(f"Exception caught, retrying... {e}")
                else:
                    raise

    def click_select_button(self):
        for attempt in range(3):
            try:
                select_button = self.page.locator("//button[normalize-space()='Select']").first
                select_button.wait_for(state="visible", timeout=10000)
                select_button.click()
                break
            except Exception as e:
                if attempt < 2:
                    print(f"Exception caught, retrying... {e}")
                else:
                    raise

    def select(self):
        self.choose_customer()
        self.click_select_button()
