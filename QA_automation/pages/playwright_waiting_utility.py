import platform
import time
from typing import List

from playwright.sync_api import Page, TimeoutError as PlaywrightTimeoutError


class PlaywrightWaitingUtility:
    def __init__(self, page: Page, timeout=30000):  # Playwright uses milliseconds
        self.page = page
        self.timeout = timeout

    def element_exists(self, by, value):
        """
        This method checks if an element exists on the page.

        :param by: The locator strategy to use (ignored for <PERSON><PERSON>, kept for compatibility).
        :param value: The locator value to use (XPath or CSS selector).
        :return: True if the element exists, False otherwise
        """
        try:
            # Convert XPath to Playwright locator
            locator = self._convert_locator(value)
            self.page.locator(locator).wait_for(state="attached", timeout=1000)
            return True
        except PlaywrightTimeoutError:
            return False

    def _convert_locator(self, value):
        """
        Convert Selenium locator to Playwright locator.
        For XPath, return as-is. For other selectors, may need conversion.
        """
        if value.startswith("//") or value.startswith("(//"):
            return value  # XPath
        elif value.startswith("#"):
            return value  # CSS ID selector
        elif value.startswith("."):
            return value  # CSS class selector
        else:
            # Assume it's an ID without # prefix
            return f"#{value}" if not value.startswith("[") else value

    def wait_for_element(self, by, value):
        """
        This method waits for an element to be present.

        :param by: The locator strategy to use (ignored for Playwright, kept for compatibility).
        :param value: The locator value to use.
        :return: The element locator if it is present. Otherwise, raises a TimeoutError.
        """
        locator = self._convert_locator(value)
        element = self.page.locator(locator)
        element.wait_for(state="attached", timeout=self.timeout)
        return element

    def wait_for_element_text(self, by, value, text):
        """
        This method waits for an element to be present,
        and for the text of the element to be equal to the provided text.

        :param by: The locator strategy to use (ignored for Playwright, kept for compatibility).
        :param value: The locator value to use.
        :param text: The text to wait for.
        :return: True if the element has the expected text. Otherwise, raises a TimeoutError.
        """
        locator = self._convert_locator(value)
        element = self.page.locator(locator)
        element.wait_for(state="visible", timeout=self.timeout)
        self.page.wait_for_function(
            f"element => element.textContent.includes('{text}')",
            element.element_handle(),
            timeout=self.timeout
        )
        return True

    def wait_for_element_if_clickable_and_click(self, by, value):
        """
        This method waits for an element to be displayed and clickable.
        If it is clickable it performs a click.

        :param by: The locator strategy to use (ignored for Playwright, kept for compatibility).
        :param value: The locator value to use.
        :return: True if clicked, False otherwise.
        """
        try:
            locator = self._convert_locator(value)
            element = self.page.locator(locator)
            element.wait_for(state="visible", timeout=self.timeout)
            element.click()
            return True
        except PlaywrightTimeoutError:
            return False

    def click_on_element_avoid_stale_element(self, by, value, retries=5):
        """
        This method waits for an element to be displayed and clickable.
        If it is clickable it performs a click.

        This method is expensive, should be avoided unless we are dealing with extensive re-rendering.

        :param by: The locator strategy to use (ignored for Playwright, kept for compatibility).
        :param value: The locator value to use.
        :param retries: The number of times to retry if the element is not displayed.
        :return: True if clicked, False otherwise.
        """
        for _ in range(retries):
            element_clicked = self.wait_for_element_if_clickable_and_click(by, value)
            if element_clicked:
                return True
        return False

    def wait_for_input_send_keys_avoid_stale_element(self, by, value, keys, retries=5):
        """
        This method waits for an element to be displayed.
        If it is displayed it returns the element.

        This method is expensive, should be avoided unless we are dealing with extensive re-rendering.

        :param by: The locator strategy to use (ignored for Playwright, kept for compatibility).
        :param value: The locator value to use.
        :param keys: The keys to send to the element.
        :param retries: The number of times to retry if the element is not displayed.
        :return: True if clicked, False otherwise.
        """
        for _ in range(retries):
            try:
                locator = self._convert_locator(value)
                element = self.page.locator(locator)
                element.wait_for(state="visible", timeout=self.timeout)
                element.fill(keys)
                return True
            except PlaywrightTimeoutError as e:
                print(f"while using wait_for_input_send_keys_avoid_stale_element: {e}")
                if _ == retries - 1:  # Last retry
                    return False
        return False

    def wait_until_dcoya_table_loaded(self, table_id):
        """
        This method waits for a Dcoya table to load. The table is considered loaded when the table with id {
        table_id}_loaded is displayed.
        """
        try:
            locator = f"#{table_id}_loaded"
            element = self.page.locator(locator)
            element.wait_for(state="visible", timeout=self.timeout)
            return element.is_visible()
        except PlaywrightTimeoutError as e:
            raise PlaywrightTimeoutError(f"Table with id {table_id} did not load in time.\n{e}")

    def wait_until_dcoya_system_button_not_in_cooldown(self, button_id):
        """
        This method waits for a Dcoya System Button to not be in cooldown. The button is considered not in cooldown when
        the button with id {button_id}_cooldown-false is displayed.

        :param button_id: The id of the button.
        :return: The button locator if it is displayed, otherwise raises a TimeoutError.
        """
        try:
            locator = f"//button[@id='{button_id}_cooldown-false']"
            element = self.page.locator(locator)
            element.wait_for(state="visible", timeout=self.timeout)
            return element
        except PlaywrightTimeoutError as e:
            raise PlaywrightTimeoutError(f"dcoya_system_button with id {button_id} did not load in time.\n{e}")

    def wait_until_template_grid(self, template_grid_id="template-grid", loading=False, expect_empty=False):
        """
        This method waits for a Template Grid to load. The Grid is considered loaded when the grid with id {
        template_grid_id}_loaded is displayed.

        :param template_grid_id: The id of the template grid.
        :param loading: If True, the method waits for the grid to be loading. If False, the method waits for the grid to
            be loaded.
        :param expect_empty: If True, the method waits for the grid to be empty.
        :return: True if the grid is displayed, False otherwise.
        """
        try:
            empty_str = "_empty" if expect_empty else ""
            locator = f"#{template_grid_id}_load{'ing' if loading else f'ed{empty_str}'}"
            element = self.page.locator(locator)
            element.wait_for(state="visible", timeout=self.timeout)
            return element.is_visible()
        except PlaywrightTimeoutError as e:
            raise PlaywrightTimeoutError(
                f"Grid with id {template_grid_id} did not load in time. Requested loading: {loading} Expected Empty: {expect_empty}\n{e}")

    def wait_and_hover_on_element(self, by, value):
        """
        This method waits for an element to be present.
        :param by: The locator strategy to use (ignored for Playwright, kept for compatibility).
        :param value: The locator value to use.
        :return: The element locator if it is present. Otherwise, returns None.
        """
        try:
            locator = self._convert_locator(value)
            element = self.page.locator(locator)
            element.wait_for(state="visible", timeout=self.timeout)
            element.hover()
            return element
        except PlaywrightTimeoutError:
            return None

    def dropdown_click(self, dropdown_id, use_stale_element=False):
        """
        This method waits for a dropdown to be present and clicks on it.
        :param dropdown_id: The id of the dropdown.
        :param use_stale_element: If True, the method will try to click on the element avoiding a stale element.
        :return: True if the dropdown was clicked, False otherwise.
        """
        try:
            dropdown_xpath = f"//*[@id='{dropdown_id}_dcoya-drop-down-input']"

            if use_stale_element:
                self.click_on_element_avoid_stale_element(None, dropdown_xpath)
            else:
                element = self.page.locator(dropdown_xpath)
                element.wait_for(state="visible", timeout=self.timeout)
                element.click()
            return True
        except PlaywrightTimeoutError:
            return False

    def perform_search_in_dcoya_search_bar(self, search_keyword, search_input_id="search_bar", perform_clear=False,
                                           debounce=0.5):
        """
        This method waits for a Dcoya search input to be present and performs a search.
        :param search_keyword: The keyword to search.
        :param search_input_id: The id of the search input.
        :param perform_clear: If True, the method will clear the search input before sending the search keyword.
        :param debounce: The debounce time to wait after sending the search keyword.
        :return: True if the search was performed, False otherwise.
        """
        locator = f"//div[@id='DcoyaUpgradedInput_{search_input_id}']//input"
        search_input = self.page.locator(locator)
        
        try:
            search_input.wait_for(state="visible", timeout=self.timeout)
        except PlaywrightTimeoutError:
            return False

        if perform_clear:
            search_input.click()
            # Use Playwright's keyboard shortcuts
            if platform.system() == 'Darwin':  # macOS
                self.page.keyboard.press('Meta+a')  # Command+A
            else:
                self.page.keyboard.press('Control+a')  # Ctrl+A
            self.page.keyboard.press('Delete')

        # Send the search keyword
        search_input.fill(search_keyword)
        # Small delay to handle search debounce
        time.sleep(debounce)
        return True
