import os
import platform
import time


class DownloadUtility:
    @staticmethod
    def get_download_directory():
        if platform.system() == "Windows":
            return os.path.join(os.getenv('USERPROFILE'), 'Downloads')  # Windows default
        else:
            return os.path.join(os.path.expanduser('~'), 'Downloads')  # Linux/macOS default

    @staticmethod
    def wait_for_file_download(download_dir, file_name, file_ext, timeout=30):
        """
        Wait for the specified file to be downloaded.
        :param download_dir: The directory to check for the file.
        :param file_name: The name of the file to wait for.
        :param file_ext: The extension of the file to wait for.
        :param timeout: The maximum time to wait for the file to be downloaded.
        :return: The path to the downloaded file, or None if the file was not found.
        """
        end_time = time.time() + timeout
        while time.time() < end_time:
            # Get all files in the download directory
            files = [f for f in os.listdir(download_dir) if f.endswith(file_ext) and file_name in f]
            if files:
                # Sort files by modification time (newest first)
                files.sort(key=lambda x: os.path.getmtime(os.path.join(download_dir, x)), reverse=True)
                # Return the most recently modified file
                return os.path.join(download_dir, files[0])
            time.sleep(1)
        return None

    @staticmethod
    def delete_file(file_path):
        """
        Delete the specified file.
        :param file_path: The path to the file to delete.
        :return: True if the file was successfully deleted, False otherwise.
        """
        os.remove(file_path)
        return True
