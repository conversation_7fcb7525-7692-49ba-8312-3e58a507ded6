import pytest

from pages.Tenants.customers.phishing_campaign import PhishingCampaign
from .navigate_to_phishing import NavigateToPhishing
from data.campaigns import Campaigns


class TestCreateCampaignSmart:

    @pytest.mark.main
    def test_smart_campaign_create(self, setup, nemo_create_user_and_group, nemo_add_users_to_group_api):
        page = setup

        phishing_test = NavigateToPhishing(page)
        phishing_test.user_select()
        phishing_test.go_to_phishing()

        # Create smart campaign using the migrated PhishingCampaign class
        smart_campaign = PhishingCampaign(page)
        smart_campaign.create_campaign_smart()

        page.wait_for_load_state('networkidle')

        campaign_name = Campaigns.CAMPAIGN_NAME_PHISHING
        campaign_locator = f"//div[contains(@aria-label, '{campaign_name}')]"

        try:
            page.locator(campaign_locator).wait_for(state="visible", timeout=30000)
            assert page.locator(campaign_locator).is_visible(), f"Campaign '{campaign_name}' was not found in the campaigns list"
            print(f"✅ Campaign '{campaign_name}' was successfully created and is visible in the list")
        except Exception as e:
            page.screenshot(path="campaign_creation_failed.png")
            raise AssertionError(f"Campaign creation failed: {e}")

