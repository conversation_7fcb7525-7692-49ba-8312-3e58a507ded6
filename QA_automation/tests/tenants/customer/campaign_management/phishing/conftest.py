import os
from datetime import datetime, timedelta
import pytest
from requests import Session

from data.campaigns import Campaigns
from data.group_names import GroupNames
from data.users_info import UsersInfo
from data.urls import Urls

group_name = GroupNames().Group_NAME
edited_group_name = GroupNames().EDITED_GROUP_NAME
start_date = datetime.now().strftime('%Y-%m-%d')
end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
employee_email_address = UsersInfo.email_address
employee_first_name = UsersInfo.first_name
employee_last_name = UsersInfo.last_name
domain = UsersInfo.domain

SUBJECT_INPUT_SELECTOR = "#DcoyaUpgradedInput_subjectInput input"
FROM_EMAIL_INPUT_SELECTOR = "#DcoyaUpgradedInput_fromEmailInput input"
FROM_NAME_INPUT_SELECTOR = "#DcoyaUpgradedInput_sentFromInput input"


@pytest.fixture()
def get_test_group_id(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(
        f"{Urls.HOST}/api/employees/group/get_list?order_dir=desc&search_value={group_name}",
        headers=auth_data['headers']
    )

    try:
        return response.json()[0]['id']
    except:
        return


@pytest.fixture()
def get_test_user_id(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/get_list?items_per_page=20&order_by=id&order_dir=desc&page=1"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(API_URL, headers=auth_data['headers'])
    response_data = response.json()
    employees = response_data.get('data', [])

    target_email = employee_email_address
    matching_employee = next((emp for emp in employees if emp['email'] == target_email), None)

    if matching_employee:
        matching_id = matching_employee['id']
        print([matching_id])
        return [matching_id]

    try:
        return response.json()[0]['id']
    except:
        return None


@pytest.fixture()
def nemo_group_create_api(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "name": group_name,
        "tags": []
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_group_delete_api(auth_data, get_test_group_id):
    if not get_test_group_id:
        return  # group not exist, nothing to delete.
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "id": get_test_group_id
    }

    response = session.delete(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_create_user_api(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "first_name": employee_first_name,
        "last_name": employee_last_name,
        "email": employee_email_address,
        "active": 1,
        "phone": "",
        "department": "",
        "branch": "",
        "job_title": "",
        "country": "",
        "manager": "",
        "preferred_language": 1
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        user_id = response.json().get('id', None)
    except:
        user_id = None

    return {
        "status_code": response.status_code,
        "user_id": user_id
    }


@pytest.fixture()
def nemo_delete_user_api(auth_data, get_test_user_id):
    if not get_test_user_id:
        return
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = get_test_user_id

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_create_user_and_group(auth_data, nemo_delete_user_api, nemo_group_delete_api, nemo_create_user_api,
                               nemo_group_create_api):
    pass


@pytest.fixture()
def nemo_add_users_to_group_api(auth_data, get_test_group_id, get_test_user_id):
    if not get_test_group_id:
        return
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/assign_employees"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "group_id": get_test_group_id,
        "employees_ids": get_test_user_id
    }

    response = session.put(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def get_campaign_name():
    campaign_name = Campaigns.CAMPAIGN_NAME_PHISHING
    return campaign_name


@pytest.fixture()
def nemo_create_new_campaign_api(auth_data, get_test_group_id, get_campaign_name):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaign/add"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)
        data = {
            "close_campaign_after_end": False,
            "closing_campaign_config": Campaigns.CLOSING_CAMPAIGN_CONFIG,
            "closing_delay": 3,
            "end": end_date,
            "name": get_campaign_name,
            "report_days": "1,1,1,1,1,0,0",
            "report_enable": False,
            "report_time": "09:00:00",
            "report_type": None,
            "start": start_date,
            "type": 1
        }

    try:
        response = session.post(API_URL, json=data, headers=auth_data['headers'])
        response.raise_for_status()
        response_json = response.json()
        campaign_id = response_json.get('campaignId')
        print(f"Campaign Name: {get_campaign_name}")
        print(f"Created Campaign ID: {campaign_id}")
        return {"campaign_id": campaign_id, "campaign_name": get_campaign_name}

    except:
        return


@pytest.fixture()
def get_campaigns_containing_name(auth_data, search_name=Campaigns.CAMPAIGN_NAME_PHISHING):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaigns/"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    try:
        response = session.get(API_URL, headers=auth_data['headers'])
        response.raise_for_status()
        response_json = response.json()
        campaigns = response_json.get('campaigns', [])

        matching_campaigns = {}

        if isinstance(campaigns, list):
            for campaign in campaigns:
                if isinstance(campaign, dict):
                    campaign_name = campaign.get('name', "")
                    campaign_id = campaign.get('campaignId')

                    if search_name.lower() in campaign_name.lower():
                        matching_campaigns[campaign_name] = campaign_id

        if matching_campaigns:
            for name, campaign_id in matching_campaigns.items():
                print(f"Campaign Name: {name}, Campaign ID: {campaign_id}")
        else:
            print("No matching campaigns found.")

        return matching_campaigns if matching_campaigns else None
    except Exception as e:
        print(f"Error occurred: {e}")
        return None


@pytest.fixture()
def get_campaign_id(auth_data, nemo_create_new_campaign_api, get_campaign_name):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaigns/"

    campaign_name = get_campaign_name

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    try:
        response = session.get(API_URL, headers=auth_data['headers'])
        response.raise_for_status()
        response_json = response.json()
        campaigns = response_json.get('campaigns', [])

        if isinstance(campaigns, list):
            for campaign in campaigns:
                if isinstance(campaign, dict) and campaign.get('name') == campaign_name:
                    campaign_id = campaign.get('campaignId')
                    print(f"Found Campaign ID: {campaign_id}")

                    return campaign_id
    except:
        return None


@pytest.fixture()
def get_previous_simulation_id(auth_data):
    session = Session()
    API_URL = (f"{Urls.HOST}/api/simulations/crud_serve_react?items_per_page=5&order_by=id&order_dir=desc&page=1"
               "&is_aware=false&show_deleted=false")
    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)
    try:
        response = session.get(API_URL, headers=auth_data['headers'])
        data = response.json().get('data', [])
        if len(data) < 2:
            print("Not enough IDs to determine the second largest.")
            return None
        ids = [item['id'] for item in data]
        second_largest_id = sorted(ids, reverse=True)[1]
        print(f"Second Largest Simulation ID: {second_largest_id}")
        return second_largest_id

    except Exception as e:
        print(f"An error occurred: {e}")
        raise


@pytest.fixture()
def get_simulation_id(auth_data):
    session = Session()
    API_URL = (f"{Urls.HOST}/api/simulations/crud_serve_react?items_per_page=5&order_by=id&order_dir=desc&page=1"
               f"&is_aware=false&show_deleted=false")

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)
    try:
        response = session.get(API_URL, headers=auth_data['headers'])
        data = response.json().get('data', [])
        ids = [item['id'] for item in data]
        simulation_id = sorted(ids, reverse=True)[0]
        print(f"Simulation ID: {simulation_id}")
        return simulation_id

    except Exception as e:

        print(f"An error occurred: {e}")
        raise


@pytest.fixture()
def nemo_delete_simulation_phishing(auth_data, get_simulation_id):
    session = Session()
    API_URL = f"{Urls.HOST}/api/simulations/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {"simsId": [get_simulation_id]}

    print(f"Attempting to delete simulation with ID: {get_simulation_id}")
    try:
        response = session.post(API_URL, json=data, headers=auth_data['headers'])
        response.raise_for_status()
        response_text = response.text
        print(f"Response text: {response_text}")

        if response_text:
            response_json = response.json()
            deleted_id = response_json.get('simulationId')
            print(f"Successfully deleted simulation with ID: {deleted_id}")
            return {"simsId": [deleted_id]}
        else:
            print("Empty response received")
            return None
    except:
        return None


@pytest.fixture()
def nemo_delete_new_campaign(auth_data, get_campaign_id):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaign/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "campaignId": get_campaign_id
    }

    print(f"Attempting to delete campaign with ID: {get_campaign_id}")
    try:
        response = session.post(API_URL, json=data, headers=auth_data['headers'])
        response.raise_for_status()
        response_text = response.text
        print(f"Response text: {response_text}")

        if response_text:
            response_json = response.json()
            deleted_id = response_json.get('campaignId')
            print(f"Successfully deleted campaign with ID: {deleted_id}")
            return deleted_id
        else:
            print("Empty response received")
            return None

    except:

        return None


@pytest.fixture()
def delete_all_matching_campaigns(auth_data, get_campaigns_containing_name):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaign/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    matching_campaigns = get_campaigns_containing_name

    if matching_campaigns:
        for campaign_name, campaign_id in matching_campaigns.items():
            data = {
                "campaignId": campaign_id
            }

            print(f"Attempting to delete campaign: {campaign_name} with ID: {campaign_id}")
            try:
                response = session.post(API_URL, json=data, headers=auth_data['headers'])
                response.raise_for_status()
                response_text = response.text

                if response_text:
                    response_json = response.json()
                    deleted_id = response_json.get('campaignId')
                    print(f"Successfully deleted campaign with ID: {deleted_id}")


            except:
                return None

@pytest.fixture()
def create_edited_group(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "name": edited_group_name,
        "tags": []
    }
    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def create_edited_user(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "first_name": UsersInfo.edit_first_name,
        "last_name": UsersInfo.edit_last_name,
        "email": UsersInfo.edit_email_address,
        "active": 1,
        "phone": "",
        "department": "",
        "branch": "",
        "job_title": "",
        "country": "",
        "manager": "",
        "preferred_language": 1
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        user_id = response.json().get('id', None)
    except:
        user_id = None

    return {
        "status_code": response.status_code,
        "user_id": user_id
    }


@pytest.fixture()
def delete_edited_group(auth_data, get_edited_group_id):
    if not get_edited_group_id:
        return 
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "id": get_edited_group_id
    }

    response = session.delete(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def delete_edited_user(auth_data, get_edited_user_id):
    if not get_edited_user_id:
        return
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = get_edited_user_id

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None
@pytest.fixture()
def get_edited_group_id(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(
        f"{Urls.HOST}/api/employees/group/get_list?order_dir=desc&search_value={edited_group_name}",
        headers=auth_data['headers']
    )

    try:
        return response.json()[0]['id']
    except:
        return


@pytest.fixture()
def get_edited_user_id(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/get_list?items_per_page=20&order_by=id&order_dir=desc&page=1"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(API_URL, headers=auth_data['headers'])
    response_data = response.json()
    employees = response_data.get('data', [])

    target_email = UsersInfo.edit_email_address
    matching_employee = next((emp for emp in employees if emp['email'] == target_email), None)

    if matching_employee:
        matching_id = matching_employee['id']
        print([matching_id])
        return [matching_id]

    try:
        return response.json()[0]['id']
    except:
        return None

@pytest.fixture()
def nemo_add_edited_users_to_group_api(auth_data, get_edited_group_id, get_edited_user_id):
    if not get_edited_group_id:
        return
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/assign_employees"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "group_id": get_edited_group_id,
        "employees_ids": get_edited_user_id
    }

    response = session.put(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def complete_functionality_for_edited_user_and_group(auth_data, delete_edited_user, delete_edited_group,
                                                     create_edited_user, create_edited_group,
                                                     ):
    pass



