import logging

import pytest

from pages.Tenants.customers.phishing_campaign import PhishingCampaign
from .....tenants.customer.campaign_management.phishing.navigate_to_phishing import NavigateToPhishing


class TestDeleteSmartCampaign:
    @pytest.mark.main
    def tes_delete(self):
        pass

    @pytest.mark.main
    def test_delete_campaign_smart(self, setup, delete_all_matching_campaigns, get_campaign_id):
        phishing_test = NavigateToPhishing(setup)
        phishing_test.user_select()
        phishing_test.go_to_phishing()
        smart_campaign = PhishingCampaign(setup)
        smart_campaign.delete_campaign()
