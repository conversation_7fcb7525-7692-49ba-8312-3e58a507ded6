import pytest
import allure

from ......tenants.customer.campaign_management.phishing.navigate_to_phishing import NavigateToPhishing
from pages.Tenants.customers.simulation_phishing import SimulationPhishing

class TestCloneSimulationPhishing:
    """Test class for phishing simulation cloning functionality."""

    @allure.feature("Phishing Simulation")
    @allure.story("Clone Simulation")
    @allure.issue("https://ninjio.atlassian.net/browse/SAOP2-155", "Jira Ticket SAOP2-155")
    @pytest.mark.main
    def test_create_phishing_simulation(self, setup, nemo_create_user_and_group,
                                        nemo_add_users_to_group_api,
                                        delete_all_matching_campaigns,
                                        nemo_delete_simulation_phishing,
                                        get_campaign_id):
        """Test creating a new phishing simulation."""
        # Initialize navigation to phishing module
        phishing_test = NavigateToPhishing(setup)
        phishing_test.user_select()
        phishing_test.go_to_phishing()

        # Initialize phishing simulation test
        phishing_simulation_test = SimulationPhishing(setup)
        phishing_simulation_test.campaign_for_tested_simulation()
        phishing_simulation_test.create_new_simulation_phishing()
        phishing_simulation_test.clone_simulation_phishing()