import pytest
from ......tenants.customer.campaign_management.phishing.navigate_to_phishing import NavigateToPhishing
from pages.Tenants.customers.simulation_phishing import SimulationPhishing

"""
Create new Phishing simulation:
In this test, I will create a new Phishing 
simulation under a Phishing campaign.
It will test the creation of the simulation
using the "New" tab inside the tested campaign.

Improvements added to the exists test: 
1.Create group with employee api.
2.Create new campaign dedicated for the 
test.
3.Run the selenium test inside the tested campaign for creating new simulation phishing that includes the group 
with the tested employee.
4.Before running test, added the functionality of deleting simulation if exists and also 
campaign for clean start."""


class TestCreatePhishingSimulation:

    @pytest.mark.main
    def test_create_phishing_simulation(self, setup, nemo_create_user_and_group, nemo_add_users_to_group_api,
                                        delete_all_matching_campaigns,
                                        nemo_delete_simulation_phishing,
                                        get_campaign_id):
        phishing_test = NavigateToPhishing(setup)
        phishing_test.user_select()
        phishing_test.go_to_phishing()

        phishing_simulation_test = SimulationPhishing(setup)
        phishing_simulation_test.campaign_for_tested_simulation()
        phishing_simulation_test.create_new_simulation_phishing()

    @pytest.mark.main
    def test_verify_simulation_created(self, get_previous_simulation_id, get_simulation_id):
        previous_id = get_previous_simulation_id
        current_id = get_simulation_id

        assert current_id > previous_id, (
            f"Test failed: {current_id} is not larger than {previous_id},No new "
            f"simulation was created"
        )
        print("Test passed: New simulation id was created successfully")