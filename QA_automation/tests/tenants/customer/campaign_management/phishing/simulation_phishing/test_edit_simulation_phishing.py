import time

import pytest

from pages.Tenants.customers.edit_simulation_phishing import EditSimulationPhishing
from ......tenants.customer.campaign_management.phishing.navigate_to_phishing import NavigateToPhishing
from pages.Tenants.customers.simulation_phishing import SimulationPhishing

"""
Edit Phishing simulation:
In this test, I will edit a Phishing 
simulation under a Phishing campaign.
It will test the editing of the simulation
using the "Edit" tab inside the tested campaign.

"""


class TestEditPhishingSimulation:
    @pytest.mark.main
    def test_create_phishing_simulation(self, setup, nemo_create_user_and_group, nemo_add_users_to_group_api,
                                        delete_all_matching_campaigns,
                                        nemo_delete_simulation_phishing,
                                        get_campaign_id):
            phishing_test = NavigateToPhishing(setup)
            phishing_test.user_select()
            phishing_test.go_to_phishing()
            phishing_simulation_test = SimulationPhishing(setup)
            phishing_simulation_test.campaign_for_tested_simulation()
            phishing_simulation_test.create_new_simulation_phishing()


    @pytest.mark.main
    def test_create_user_and_group_edited(self,complete_functionality_for_edited_user_and_group):
        pass

    @pytest.mark.main
    def test_add_users_to_group_edited(self,nemo_add_edited_users_to_group_api):
        pass



    @pytest.mark.main
    def test_edit_simulation_phishing(self,setup):
        phishing_simulation_test = EditSimulationPhishing(setup)
        phishing_simulation_test.edit_simulation()

