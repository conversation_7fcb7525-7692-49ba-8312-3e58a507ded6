import pytest

from pages.Tenants.customers.sense import Sense
from .....tenants.customer.campaign_management.training.navigate_to_training import NavigateToTraining


class TestCreateCampaignSense:

    @pytest.mark.main
    def test_create_sense_campaign(self, setup, nemo_create_user_and_group, nemo_add_users_to_group_api):
        training_test = NavigateToTraining(setup)
        training_test.user_select()
        training_test.go_to_training()
        sense_campaign = Sense(setup)
        sense_campaign.create_campaign()
