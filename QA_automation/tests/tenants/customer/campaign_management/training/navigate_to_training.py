from pages.Tenants.customers.campaign_management import CampaignManagement
from .....tenants.customer.base_select_customer import BaseSelectCustomer


class NavigateToTraining(BaseSelectCustomer):
    def user_select(self):
        self.click_multi_tenant_properties_button()
        self.click_on_selected_customer()

    def go_to_training(self):
        campaign_management = CampaignManagement(self.page)
        campaign_management.training_page()
