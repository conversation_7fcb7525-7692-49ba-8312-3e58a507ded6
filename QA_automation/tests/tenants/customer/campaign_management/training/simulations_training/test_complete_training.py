from time import sleep

import pytest

from pages.Tenants.customers.simulation_training import SimulationTraining
from ..navigate_to_training import NavigateToTraining

"""
In this test I will test the new ack button for
testing the option of complete training for tested employee user in a group .
This test will first create new group and with simulation inside .
It will enter to status window and run on the tested employee user the complete training action

"""


class TestCompleteTraining:

    @pytest.mark.main
    def test_create_group_and_user(self, nemo_create_user_and_group):
        assert isinstance(nemo_create_user_and_group, list), "Result should be a list"
        assert all(isinstance(i, int) for i in nemo_create_user_and_group), "All elements should be integers"
        assert len(nemo_create_user_and_group) > 0, "List should not be empty"

        print("User and group created successfully!")

    @pytest.mark.main
    def test_create_training_simulation(self, setup, nemo_add_users_to_group_api,
                                        delete_all_matching_campaigns,
                                        nemo_delete_simulation_training,
                                        get_campaign_id):
        training_test = NavigateToTraining(setup)
        training_test.user_select()
        training_test.go_to_training()
        simulation_test = SimulationTraining(setup)
        simulation_test.campaign_for_tested_simulation()
        simulation_test.create_new_simulation_training()

    @pytest.mark.main
    def test_start_simulation_training(self, nemo_start_simulation_training_api):
        assert isinstance(nemo_start_simulation_training_api, int), "Simulation ID should be an Integer"
        print(f"Test passed! Simulation ID: {nemo_start_simulation_training_api}")

    @pytest.mark.main
    def test_Acknowledge_complete_training(self, setup, nemo_complete_simulation_api):
        assert isinstance(nemo_complete_simulation_api, int), "Simulation ID should be an integer"
        print(f"Test passed! Acknowledged Simulation ID: {nemo_complete_simulation_api}")
