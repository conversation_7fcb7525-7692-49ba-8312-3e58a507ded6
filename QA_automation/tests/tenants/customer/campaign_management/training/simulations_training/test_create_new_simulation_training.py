import pytest

from ......tenants.customer.campaign_management.training.navigate_to_training import NavigateToTraining
from pages.Tenants.customers.simulation_training import SimulationTraining

"""
Create new training simulation:
In this test I will create new training
simulation under training campaign.
It will test the creation of the simulation
using the "New" tab inside the tested campaign.

"""


class TestCreateNewSimulationTraining:

    @pytest.mark.main
    def test_create_training_simulation(self, setup, nemo_create_user_and_group, nemo_add_users_to_group_api,
                                        delete_all_matching_campaigns,
                                        nemo_delete_simulation_training,
                                        get_campaign_id):
        training_test = NavigateToTraining(setup)
        training_test.user_select()
        training_test.go_to_training()
        simulation_test = SimulationTraining(setup)
        simulation_test.campaign_for_tested_simulation()
        simulation_test.create_new_simulation_training()
