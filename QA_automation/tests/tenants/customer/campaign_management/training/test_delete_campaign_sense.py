import time

import pytest

from pages.Tenants.customers.playwright_sense import PlaywrightSense
from .....tenants.customer.campaign_management.training.navigate_to_training import NavigateToTraining


class TestDeleteCampaignSense:

    @pytest.mark.main
    def test_delete_campaign(self, setup, nemo_create_user_and_group,
                             nemo_add_users_to_group_api, delete_all_matching_campaigns, get_campaign_id):
        page = setup

        # Navigate to training page
        training_test = NavigateToTraining(page)
        training_test.user_select()
        training_test.go_to_training()

        # Delete SENSE campaign
        sense_campaign = PlaywrightSense(page)
        sense_campaign.delete_campaign()

        # Verify deletion was successful
        print("✅ Campaign deletion test completed successfully")

