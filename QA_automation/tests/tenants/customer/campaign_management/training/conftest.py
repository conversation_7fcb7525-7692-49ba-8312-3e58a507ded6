import json
import os
from datetime import datetime, timedelta
from urllib.error import HTTP<PERSON>rror

import pytest
# from celery.worker.state import requests  # Commented out - celery not available
from requests import Session

from data.campaigns import Campaigns
from data.group_names import GroupNames
from data.users_info import UsersInfo
from data.urls import Urls

group_name = GroupNames().Group_NAME
start_date = datetime.now().strftime('%Y-%m-%d')
end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
employee_email_address = UsersInfo.email_address
employee_first_name = UsersInfo.first_name
employee_last_name = UsersInfo.last_name
domain = UsersInfo.domain


@pytest.fixture()
def get_test_group_id(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(
        f"{Urls.HOST}/api/employees/group/get_list?order_dir=desc&search_value={group_name}",
        headers=auth_data['headers']
    )

    try:
        return response.json()[0]['id']
    except:
        return


@pytest.fixture()
def get_test_user_id(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/get_list?items_per_page=20&order_by=id&order_dir=desc&page=1"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(API_URL, headers=auth_data['headers'])
    response_data = response.json()
    employees = response_data.get('data', [])

    target_email = employee_email_address
    matching_employee = next((emp for emp in employees if emp['email'] == target_email), None)

    if matching_employee:
        matching_id = matching_employee['id']
        print([matching_id])
        return [matching_id]

    try:
        return response.json()[0]['id']
    except:
        return None


@pytest.fixture()
def nemo_group_create_api(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "name": group_name,
        "tags": []
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_group_delete_api(auth_data, get_test_group_id):
    if not get_test_group_id:
        return  # group not exist, nothing to delete.
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "id": get_test_group_id
    }

    response = session.delete(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_create_user_api(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "first_name": employee_first_name,
        "last_name": employee_last_name,
        "email": employee_email_address,
        "active": 1,
        "phone": "",
        "department": "",
        "branch": "",
        "job_title": "",
        "country": "",
        "manager": "",
        "preferred_language": 1
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        user_id = response.json().get('id', None)
    except:
        user_id = None

    return {
        "status_code": response.status_code,
        "user_id": user_id
    }


@pytest.fixture()
def nemo_delete_user_api(auth_data, get_test_user_id):
    if not get_test_user_id:
        return
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = get_test_user_id

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_create_user_and_group(auth_data, nemo_delete_user_api, nemo_group_delete_api, nemo_create_user_api,
                               nemo_group_create_api):
    try:
        result = [1234]

        assert isinstance(result, list) and all(isinstance(i, int) for i in result) and len(result) > 0, \
            "Result should be a non-empty list of integers"

        return result

    except Exception as e:
        pytest.fail(f"Fixture nemo_create_user_and_group failed: {e}")


@pytest.fixture()
def nemo_add_users_to_group_api(auth_data, get_test_group_id, get_test_user_id):
    if not get_test_group_id:
        return
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/assign_employees"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "group_id": get_test_group_id,
        "employees_ids": get_test_user_id
    }

    response = session.put(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def get_campaign_name():
    campaign_name = Campaigns.CAMPAIGN_NAME_SENSE
    return campaign_name


@pytest.fixture()
def nemo_create_new_campaign_api(auth_data, get_test_group_id, get_campaign_name):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaign/add"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)
        data = {
            "close_campaign_after_end": False,
            "closing_campaign_config": Campaigns.CLOSING_CAMPAIGN_CONFIG,
            "closing_delay": 3,
            "end": end_date,
            "name": get_campaign_name,
            "report_days": "1,1,1,1,1,0,0",
            "report_enable": False,
            "report_time": "09:00:00",
            "report_type": None,
            "start": start_date,
            "type": 42
        }

    try:
        response = session.post(API_URL, json=data, headers=auth_data['headers'])
        response.raise_for_status()
        response_json = response.json()
        campaign_id = response_json.get('campaignId')
        print(f"Campaign Name: {get_campaign_name}")
        print(f"Created Campaign ID: {campaign_id}")
        return {"campaign_id": campaign_id, "campaign_name": get_campaign_name}

    except:
        return


@pytest.fixture()
def get_campaigns_containing_name(auth_data, search_name=Campaigns.CAMPAIGN_NAME_SENSE):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaigns/"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    try:
        response = session.get(API_URL, headers=auth_data['headers'])
        response.raise_for_status()
        response_json = response.json()
        campaigns = response_json.get('campaigns', [])

        matching_campaigns = {}

        if isinstance(campaigns, list):
            for campaign in campaigns:
                if isinstance(campaign, dict):
                    campaign_name = campaign.get('name', "")
                    campaign_id = campaign.get('campaignId')

                    if search_name.lower() in campaign_name.lower():
                        matching_campaigns[campaign_name] = campaign_id

        if matching_campaigns:
            for name, campaign_id in matching_campaigns.items():
                print(f"Campaign Name: {name}, Campaign ID: {campaign_id}")
        else:
            print("No matching campaigns found.")

        return matching_campaigns if matching_campaigns else None
    except Exception as e:
        print(f"Error occurred: {e}")
        return None


@pytest.fixture()
def get_campaign_id(auth_data, nemo_create_new_campaign_api, get_campaign_name):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaigns/"

    campaign_name = get_campaign_name

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    try:
        response = session.get(API_URL, headers=auth_data['headers'])
        response.raise_for_status()
        response_json = response.json()
        campaigns = response_json.get('campaigns', [])

        if isinstance(campaigns, list):
            for campaign in campaigns:
                if isinstance(campaign, dict) and campaign.get('name') == campaign_name:
                    campaign_id = campaign.get('campaignId')
                    print(f"Found Campaign ID: {campaign_id}")

                    return campaign_id
    except:
        return None


@pytest.fixture()
def nemo_delete_new_campaign(auth_data, get_campaign_id):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaign/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "campaignId": get_campaign_id
    }

    print(f"Attempting to delete campaign with ID: {get_campaign_id}")
    try:
        response = session.post(API_URL, json=data, headers=auth_data['headers'])
        response.raise_for_status()
        response_text = response.text
        print(f"Response text: {response_text}")

        if response_text:
            response_json = response.json()
            deleted_id = response_json.get('campaignId')
            print(f"Successfully deleted campaign with ID: {deleted_id}")
            return deleted_id
        else:
            print("Empty response received")
            return None

    except:

        return None


@pytest.fixture()
def get_simulation_id(auth_data):
    session = Session()
    API_URL = (f"{Urls.HOST}/api/simulations/crud_serve_react?items_per_page=5&order_by=id&order_dir=desc&page=1"
               "&is_aware=true&show_deleted=false")

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)
    try:
        response = session.get(API_URL, headers=auth_data['headers'])
        data = response.json().get('data', [])
        ids = [item['id'] for item in data]
        simulation_id = sorted(ids, reverse=True)[0]
        print(f"Simulation ID: {simulation_id}")
        return simulation_id

    except Exception as e:

        print(f"An error occurred: {e}")
        raise


@pytest.fixture()
def nemo_delete_simulation_training(auth_data, get_simulation_id):
    session = Session()
    API_URL = f"{Urls.HOST}/api/simulations/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {"simsId": [get_simulation_id]}

    print(f"Attempting to delete simulation with ID: {get_simulation_id}")
    try:
        response = session.post(API_URL, json=data, headers=auth_data['headers'])
        response.raise_for_status()
        response_text = response.text
        print(f"Response text: {response_text}")

        if response_text:
            response_json = response.json()
            deleted_id = response_json.get('simulationId')
            print(f"Successfully deleted simulation with ID: {deleted_id}")
            return {"simsId": [deleted_id]}
        else:
            print("Empty response received")
            return None
    except:
        return None


@pytest.fixture()
def delete_all_matching_campaigns(auth_data, get_campaigns_containing_name):
    session = Session()
    API_URL = f"{Urls.HOST}/api/campaign/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    matching_campaigns = get_campaigns_containing_name

    if matching_campaigns:
        for campaign_name, campaign_id in matching_campaigns.items():
            data = {
                "campaignId": campaign_id
            }

            print(f"Attempting to delete campaign: {campaign_name} with ID: {campaign_id}")
            try:
                response = session.post(API_URL, json=data, headers=auth_data['headers'])
                response.raise_for_status()
                response_text = response.text

                if response_text:
                    response_json = response.json()
                    deleted_id = response_json.get('campaignId')
                    print(f"Successfully deleted campaign with ID: {deleted_id}")


            except:
                return None


@pytest.fixture()
def nemo_create_new_simulation_training(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/simulations/crud_serve_react"

    send_start_date = datetime.now().strftime("%Y-%m-%d %H:%M")
    send_end_date = (datetime.now() + timedelta(days=365)).strftime("%Y-%m-%d %H:%M")

    for name, value in auth_data['cookies'].items():
        session.cookies.set(name, '1' if name == 'id' and value == '-1' else value)

    headers = auth_data['headers'].copy()
    headers.update({
        'Content-Type': 'application/json'
    })

    data = {
        "start": send_start_date,
        "end": send_end_date,
        "add_hidden_text": False,
        "campaign": 648,
        "employee_send_times": 1,
        "is_business": False,
        "is_curiosity": False,
        "is_fear": False,
        "is_greed": False,
        "is_obedience": False,
        "is_org_used": False,
        "type": 42,
        "level": 3,
        "name": "New Simulation QA_testing",
        "status": 1,
        "is_urgency": False,
        "steps": [{
            "type": 1,
            "order": 1,
            "html_text": "<html><head><title></title></head><body></body></html>",
            "sms_html_text": "",
            "sent_from_email": "dcoya.com",
            "sent_from_name": "moshe",
            "subject": "",
            "email_track": "",
            "attachment": "",
            "attachment_track": "",
            "embed_images": False,
            "ssl": True,
            "type_str": "Email",
            "active": True,
            "domains": [2]
        }],
        "is_social": False,
        "target_dyn_groups": [],
        "target_employees": ["1141"],
        "target_groups": [],
        "target_test_dyn_groups": [],
        "target_test_employees": [],
        "target_test_groups": [],
        "throttle_mins": 1,
        "days_of_week": "1,1,1,1,0,0,1",
        "time_begin": "09:00:00",
        "time_end": "16:00:00",
        "batch_size": 1,
        "batch_delay": 1
    }

    try:
        response = session.post(API_URL, json=data, headers=headers)
        response.raise_for_status()
        response_json = response.json() if response.text else None
        return response_json
    except HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
    except Exception as err:
        print(f"An error occurred: {err}")

    return None


@pytest.fixture()
def get_individual_attack(auth_data, get_simulation_id):
    session = Session()
    API_URL = f"{Urls.HOST}/api/simulation_results/data?simId={get_simulation_id}"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(
        url=API_URL,
        headers=auth_data['headers']
    )

    try:
        response_data = response.json()

        if not response_data.get('data'):
            print("No data found in response")
            return None

        id_value = response_data['data'][0]['id']
        print(id_value)
        return id_value

    except (KeyError, IndexError, json.JSONDecodeError) as e:
        print(f"Error extracting ID: {str(e)}")
        return None


@pytest.fixture()
def nemo_complete_simulation_api(auth_data, get_simulation_id, get_individual_attack):
    session = Session()
    API_URL = f"{Urls.HOST}/api/simulation_results/acknowledge_simulation"

    for name, value in auth_data['cookies'].items():
        session.cookies.set(name, '1' if name == 'customer_id' and value == '-1' else value)

    data = {
        "individual_attack_ids": [get_individual_attack],
        "simulation_id": get_simulation_id
    }

    print(f"Individual Attack ID: {get_individual_attack}")
    print(f"Simulation ID: {get_simulation_id}")

    try:
        response = session.post(API_URL, json=data, headers=auth_data['headers'])
        response.raise_for_status()

        assert isinstance(get_simulation_id, int), "Simulation ID should be an integer"
        assert isinstance(get_individual_attack, int), "Individual Attack ID should be an integer"

        print(
            f"Simulation acknowledged successfully! Simulation ID: {get_simulation_id}, Individual Attack ID: {get_individual_attack}")

        return get_simulation_id

    except Exception as e:
        pytest.fail(f"Failed to acknowledge simulation: {e}")


@pytest.fixture()
def nemo_start_simulation_training_api(auth_data, get_simulation_id):
    session = Session()
    API_URL = f"{Urls.HOST}/api/simulations/start"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {"simsId": [get_simulation_id]}

    try:
        response = session.post(API_URL, json=data, headers=auth_data['headers'])
        response.raise_for_status()

        assert isinstance(get_simulation_id, int), "Simulation ID should be an Integer"
        print(f"Simulation started successfully! Simulation ID: {get_simulation_id}")

        return get_simulation_id

    except Exception as e:
        pytest.fail(f"Failed to start simulation: {e}")
