from logging import Logger
import pytest
from base.logger import QALogger
from pages.Tenants.customers.templates_management import TemplateType
from ....tenants.customer.template_management.navigate_to_templates import NavigateToTemplates


class TestTemplateManagement:
    @classmethod
    def setup_class(cls):
        cls.logger: Logger = QALogger("TestTemplateManagementLogger").get_logger()

    @pytest.mark.main
    def test_import_phish_template(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_phishing_templates(self.logger)
        management_class.test_import_templates(template_type=TemplateType.PHISH)

    @pytest.mark.main
    def test_export_phish_template(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_phishing_templates(self.logger)
        management_class.test_export_templates(template_type=TemplateType.PHISH)

    @pytest.mark.main
    def test_filter_phish_template_success(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_phishing_templates(self.logger)
        management_class.test_filter_templates_success(template_type=TemplateType.PHISH)

    @pytest.mark.main
    def test_phish_template_tags(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_phishing_templates(self.logger)
        management_class.test_template_tags(template_type=TemplateType.PHISH)

    @pytest.mark.main
    def test_filter_phish_template_expect_failure(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_phishing_templates(self.logger)
        management_class.test_filter_templates_fail(template_type=TemplateType.PHISH)

    @pytest.mark.main
    def test_import_email_template(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_email_templates(self.logger)
        management_class.test_import_templates(template_type=TemplateType.EMAIL)

    @pytest.mark.main
    def test_export_email_template(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_email_templates(self.logger)
        management_class.test_export_templates(template_type=TemplateType.EMAIL)

    @pytest.mark.main
    def test_filter_email_template(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_email_templates(self.logger)
        management_class.test_filter_templates_success(template_type=TemplateType.EMAIL)

    @pytest.mark.main
    def test_filter_email_template_expect_fail(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_email_templates(self.logger)
        management_class.test_filter_templates_fail(template_type=TemplateType.EMAIL)

    @pytest.mark.main
    def test_email_template_tags(self, setup):
        management_class = NavigateToTemplates(setup).navigate_to_email_templates(self.logger)
        management_class.test_template_tags(template_type=TemplateType.EMAIL)
