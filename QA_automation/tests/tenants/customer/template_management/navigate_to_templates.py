from logging import Logger
from pages.Tenants.customers.templates_management import TemplatesManagement
from ....tenants.customer.base_select_customer import BaseSelectCustomer


class NavigateToTemplates(BaseSelectCustomer):
    def __user_select(self):
        self.click_multi_tenant_properties_button(refresh_page=True)
        self.click_on_selected_customer()

    def navigate_to_phishing_templates(self, logger: Logger):
        """
        This method navigates to the phishing templates page
        :return: TemplatesManagement object
        """
        self.__user_select()
        template_management = TemplatesManagement(self.page, logger)
        template_management.navigate_to_phishing_templates()
        return template_management

    def navigate_to_email_templates(self, logger: Logger):
        """
        This method navigates to the email templates page
        :return: TemplatesManagement object
        """
        self.__user_select()
        template_management = TemplatesManagement(self.page, logger)
        template_management.navigate_to_email_templates()
        return template_management
