from logging import Logger

import pytest

from base.logger import QALogger
from pages.Tenants.customers.users_and_groups import UsersAndGroups
from ....customer.user_management.users_and_groups.navigate_to_users_and_groups import NavigateToUsersAndGroups


class TestCreateNewGroup:
    @classmethod
    def setup_class(cls):
        cls.logger: Logger = QALogger("TestCreateNewGroupLogger").get_logger()

    @pytest.mark.main
    def test_create_new_group(self, nemo_group_delete_api, setup):
        navigate_to_users_and_groups = NavigateToUsersAndGroups(setup)
        navigate_to_users_and_groups.user_select()
        navigate_to_users_and_groups.go_to_user_management(self.logger)
        users_and_groups = UsersAndGroups(setup, self.logger)
        users_and_groups.new_group_create()
