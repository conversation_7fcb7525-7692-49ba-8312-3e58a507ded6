import pytest
from logging import Logger

from base.logger import Q<PERSON>ogger
from pages.Tenants.customers.users_and_groups import UsersAndGroups
from ....customer.user_management.users_and_groups.navigate_to_users_and_groups import NavigateToUsersAndGroups


class TestDeleteUser:
    @classmethod
    def setup_class(cls):
        cls.logger: Logger = QALogger("TestDeleteUserLogger").get_logger()

    @pytest.mark.main
    def test_delete_user(self, nemo_create_user_api, setup):
        navigate_to_users_and_groups = NavigateToUsersAndGroups(setup)
        navigate_to_users_and_groups.user_select()
        navigate_to_users_and_groups.go_to_user_management(self.logger)
        users_and_groups = UsersAndGroups(setup, self.logger)
        users_and_groups.user_delete()



