import threading
import pytest


class TestDuplicateEmailEmployee:
    """
    This test will run a setup of delete and create employee first.
    After that it will run in two threads for loop an employee creation with same email address.
    if test get 200 or 201 it fails (negative test success means failure in this case).
    """

    @pytest.mark.main
    def test_delete_before_create_employee(self, nemo_delete_user_api):

        pass

    @pytest.mark.main
    def test_create_employee(self, nemo_create_user_api):

        pass

    @pytest.mark.parametrize('i', range(2))
    def test_create_duplicate_email(self, nemo_duplicate_email_negative, i):

        threads = []
        results = []

        def thread_function():
            status_code = nemo_duplicate_email_negative
            results.append(status_code)

        for j in range(2):
            thread = threading.Thread(target=thread_function)
            threads.append(thread)
            thread.start()

        for thread in threads:
            thread.join()

        for status_code in results:
            assert status_code not in [200,
                                       201], f"Test failed: Status code should not be 200 or 201, got {status_code}"

        if len(results) > 1 and results[1] in [200, 201]:
            pytest.skip(f"Second thread succeeded unexpectedly with status code: {results[1]}")
