import pytest
from requests import Session

from data.group_names import GroupNames
from data.urls import Urls
from data.users_info import UsersInfo

group_name = GroupNames().Group_NAME


@pytest.fixture()
def get_test_group_id(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(
        f"{Urls.HOST}/api/employees/group/get_list?order_dir=desc&search_value={group_name}",
        headers=auth_data['headers']
    )

    try:
        return response.json()[0]['id']
    except:
        return


@pytest.fixture()
def get_test_user_id(auth_data):
    session = Session()
    API_URL = "http://localhost/api/employees/get_list?items_per_page=20&order_by=id&order_dir=desc&page=1"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(API_URL, headers=auth_data['headers'])
    response_data = response.json()
    employees = response_data.get('data', [])

    target_email = UsersInfo.email_address
    matching_employee = next((emp for emp in employees if emp['email'] == target_email), None)

    if matching_employee:
        matching_id = matching_employee['id']
        print([matching_id])
        return [matching_id]

    try:
        return response.json()[0]['id']
    except:
        return None


@pytest.fixture()
def nemo_group_create_api(auth_data):
    session = Session()
    API_URL = "http://localhost/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "name": group_name,
        "tags": []
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_group_delete_api(auth_data, get_test_group_id):
    if not get_test_group_id:
        return
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "id": get_test_group_id
    }

    response = session.delete(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_create_user_api(auth_data):
    session = Session()
    API_URL = "http://localhost/api/employees/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "first_name": UsersInfo.first_name,
        "last_name": UsersInfo.last_name,
        "email": UsersInfo.email_address,
        "active": 1,
        "phone": "",
        "department": "",
        "branch": "",
        "job_title": "",
        "country": "",
        "manager": "",
        "preferred_language": 1
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    return response


@pytest.fixture()
def nemo_delete_user_api(auth_data, get_test_user_id):
    if not get_test_user_id:
        return
    session = Session()
    API_URL = "http://localhost/api/employees/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = get_test_user_id

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_create_user_and_group(auth_data, nemo_delete_user_api, nemo_group_delete_api, nemo_create_user_api,
                               nemo_group_create_api):
    pass


@pytest.fixture()
def nemo_add_users_to_group_api(auth_data, get_test_group_id, get_test_user_id):
    if not get_test_group_id:
        return
    session = Session()
    API_URL = "http://localhost/api/employees/group/assign_employees"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {"group_id": get_test_group_id, "employees_ids": get_test_user_id
            }

    response = session.put(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_get_log_history(auth_data, get_test_user_id):
    session = Session()
    API_URL = f"http://localhost/api/employees/get_history?employee_id={get_test_user_id[0]}"
    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(API_URL, headers=auth_data['headers'])

    try:
        log_history = response.json()
        if log_history:
            sorted_history = sorted(log_history, key=lambda x: x['last_change_timestamp'], reverse=True)
            most_recent_record = sorted_history[0]
            print(f"'id': {most_recent_record['id']}, 'active': {most_recent_record['active']}")
            return f"active: {most_recent_record['active']}"
        else:
            return "No log history found"
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


@pytest.fixture()
def nemo_get_user_status_api(auth_data, get_test_user_id):
    session = Session()
    API_URL = (f"http://localhost/api/employees/get_list?items_per_page=20&order_by=id&order_dir=desc&page=1"
               f"&search_value=moshe")

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(API_URL, headers=auth_data['headers'])

    try:
        response_data = response.json()
        if response_data and 'data' in response_data and response_data['data']:
            user_data = response_data['data'][0]
            print(f"active: {user_data['active']}")
            return f"active: {user_data['active']}"

        else:
            return "No user data found"
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


@pytest.fixture()
def nemo_duplicate_email_negative(nemo_create_user_api):
    response = nemo_create_user_api
    return response.status_code
