from logging import Logger

import pytest

from base.logger import QALogger
from pages.Tenants.customers.users_and_groups import UsersAndGroups
from ....customer.user_management.users_and_groups.navigate_to_users_and_groups import NavigateToUsersAndGroups


class TestDeleteGroup:
    @classmethod
    def setup_class(cls):
        cls.logger: Logger = QALogger("TestDeleteGroupLogger").get_logger()

    @pytest.mark.main
    def test_delete_group(self, nemo_group_create_api, setup):
        navigate_to_users_and_groups = NavigateToUsersAndGroups(setup)
        navigate_to_users_and_groups.user_select()
        navigate_to_users_and_groups.go_to_user_management(self.logger)
        users_and_groups = UsersAndGroups(setup, self.logger)
        users_and_groups.group_delete()



