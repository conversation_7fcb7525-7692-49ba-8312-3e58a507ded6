from pages.Tenants.customers.user_management import UserManagement
from ....customer.base_select_customer import BaseSelectCustomer
from logging import Logger


class NavigateToUsersAndGroups(BaseSelectCustomer):
    def user_select(self):
        self.click_multi_tenant_properties_button()
        self.click_on_selected_customer()

    def go_to_user_management(self, logger: Logger):
        user_management = UserManagement(self.page, logger)
        user_management.users_and_group_page()
