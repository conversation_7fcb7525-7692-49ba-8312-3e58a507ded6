from logging import Logger

import pytest


from base.logger import QALogger
from pages.Tenants.customers.users_and_groups import UsersAndGroups
from ....customer.user_management.users_and_groups.navigate_to_users_and_groups import NavigateToUsersAndGroups


class TestAddUsers:

    @classmethod
    def setup_class(cls):
        cls.logger: Logger = QALogger("TestAddUsersLogger").get_logger()

    @pytest.mark.main
    def test_add_users_to_group(self, nemo_create_user_and_group, setup):
        navigate_to_users_and_groups = NavigateToUsersAndGroups(setup)
        navigate_to_users_and_groups.user_select()
        navigate_to_users_and_groups.go_to_user_management(self.logger)
        users_and_groups = UsersAndGroups(setup, self.logger)
        users_and_groups.add_users()
