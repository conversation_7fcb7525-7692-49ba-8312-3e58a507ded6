import time
from logging import Logger

import pytest

from base.logger import Q<PERSON>ogger
from pages.Tenants.customers.playwright_phishing_reports import PlaywrightPhishingReports
from ....tenants.customer.reporting.navigate_to_reporting import NavigateToReporting


class TestPhishingReportsDashboard:
    @classmethod
    def setup_class(cls):
        cls.logger: Logger = QALogger("TestPhishingReportsDashboard").get_logger()

    @pytest.mark.main
    def test_select_from_dropdowns(self, setup):
        phishing_test = NavigateToReporting(setup)
        phishing_test.choose_dcoya_customer_from_tenant_choosing_table()
        phishing_test.go_to_customer_reporting_phishing(self.logger)
        reports = PlaywrightPhishingReports(setup)
        reports.phishing_dropdowns()
        print("✅ Phishing reports dashboard test completed successfully")
