from pages.Tenants.customers.reporting import Reporting
from ....tenants.customer.base_select_customer import BaseSelectCustomer
from logging import Logger


class NavigateToReporting(BaseSelectCustomer):
    def choose_dcoya_customer_from_tenant_choosing_table(self):
        self.click_multi_tenant_properties_button()
        self.click_on_selected_customer()

    def go_to_customer_reporting_phishing(self, logger: Logger):
        # Use self.page instead of self.driver for Playwright compatibility
        reporting = Reporting(self.page, logger)
        reporting.phishing_reports_page()

    def go_to_user_customer_reporting_training(self, logger: Logger):
        # Use self.page instead of self.driver for Playwright compatibility
        reporting = Reporting(self.page, logger)
        reporting.training_reports_page()
