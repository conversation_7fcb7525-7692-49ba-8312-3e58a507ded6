import time
from logging import Logger

import pytest

from base.logger import Q<PERSON>ogger
from pages.Tenants.customers.playwright_training_reports import PlaywrightPhishingReports
from ....tenants.customer.reporting.navigate_to_reporting import NavigateToReporting


class TestLearnerReport:

    @classmethod
    def setup_class(cls):
        cls.logger: Logger = QALogger("TestLearnerReport").get_logger()

    @pytest.mark.main
    def test_create_learner_report_with_setup(self, nemo_delete_learner_report_api, nemo_create_user_and_group,
                                              nemo_add_users_to_group_api, setup):
        training_reports = NavigateToReporting(setup)
        training_reports.choose_dcoya_customer_from_tenant_choosing_table()
        training_reports.go_to_user_customer_reporting_training(self.logger)
        reports = PlaywrightPhishingReports(setup, self.logger)
        reports.create_new_learner_report()
        print("✅ Learner report test completed successfully")



