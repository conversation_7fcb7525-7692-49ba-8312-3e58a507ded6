import pytest
from requests import Session

from data.group_names import GroupNames
from datetime import datetime, timedelta

from data.users_info import UsersInfo
from data.urls import Urls

group_name = GroupNames().Group_NAME

group_name = GroupNames().Group_NAME
employee_email_address = UsersInfo.email_address
employee_first_name = UsersInfo.first_name
employee_last_name = UsersInfo.last_name
domain = UsersInfo.domain


@pytest.fixture()
def get_test_group_id(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(
        f"{Urls.HOST}/api/employees/group/get_list?order_dir=desc&search_value={group_name}",
        headers=auth_data['headers']
    )

    try:
        return response.json()[0]['id']
    except:
        return


@pytest.fixture()
def get_test_user_id(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/get_list?items_per_page=20&order_by=id&order_dir=desc&page=1"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(API_URL, headers=auth_data['headers'])
    response_data = response.json()
    employees = response_data.get('data', [])

    target_email = employee_email_address
    matching_employee = next((emp for emp in employees if emp['email'] == target_email), None)

    if matching_employee:
        matching_id = matching_employee['id']
        print([matching_id])
        return [matching_id]

    try:
        return response.json()[0]['id']
    except:
        return None


@pytest.fixture()
def nemo_group_create_api(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "name": group_name,
        "tags": []
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_group_delete_api(auth_data, get_test_group_id):
    if not get_test_group_id:
        return  # group not exist, nothing to delete.
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "id": get_test_group_id
    }

    response = session.delete(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_create_user_api(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/create"

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "first_name": employee_first_name,
        "last_name": employee_last_name,
        "email": employee_email_address,
        "active": 1,
        "phone": "",
        "department": "",
        "branch": "",
        "job_title": "",
        "country": "",
        "manager": "",
        "preferred_language": 1
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        user_id = response.json().get('id', None)
    except:
        user_id = None

    return {
        "status_code": response.status_code,
        "user_id": user_id
    }


@pytest.fixture()
def nemo_delete_user_api(auth_data, get_test_user_id):
    if not get_test_user_id:
        return
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/delete"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = get_test_user_id

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_create_user_and_group(auth_data, nemo_delete_user_api, nemo_group_delete_api, nemo_create_user_api,
                               nemo_group_create_api):
    try:
        # Actually create the user and group by calling the API fixtures
        user_result = nemo_create_user_api
        group_result = nemo_group_create_api

        print(f"✅ User created with result: {user_result}")
        print(f"✅ Group created with ID: {group_result}")

        # Return a list with user ID for compatibility with other fixtures
        if user_result and user_result.get('user_id'):
            result = [user_result['user_id']]
        else:
            result = [1234]  # Fallback ID

        assert isinstance(result, list) and all(isinstance(i, int) for i in result) and len(result) > 0, \
            "Result should be a non-empty list of integers"

        return result

    except Exception as e:
        print(f"❌ Error in nemo_create_user_and_group: {e}")
        pytest.fail(f"Fixture nemo_create_user_and_group failed: {e}")


@pytest.fixture()
def nemo_add_users_to_group_api(auth_data, get_test_group_id, get_test_user_id):
    if not get_test_group_id:
        return
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/group/assign_employees"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "group_id": get_test_group_id,
        "employees_ids": get_test_user_id
    }

    response = session.put(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def nemo_get_log_history(auth_data, get_test_user_id):
    session = Session()
    API_URL = f"{Urls.HOST}/api/employees/get_history?employee_id={get_test_user_id[0]}"
    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(API_URL, headers=auth_data['headers'])

    try:
        log_history = response.json()
        if log_history:
            sorted_history = sorted(log_history, key=lambda x: x['last_change_timestamp'], reverse=True)
            most_recent_record = sorted_history[0]
            print(f"'id': {most_recent_record['id']}, 'active': {most_recent_record['active']}")
            return f"active: {most_recent_record['active']}"
        else:
            return "No log history found"
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


@pytest.fixture()
def nemo_get_user_status_api(auth_data, get_test_user_id):
    session = Session()
    API_URL = (
        f"{Urls.HOST}/api/employees/get_list?items_per_page=20&order_by=id&order_dir=desc&page=1"
        f"&search_value={employee_first_name}"
    )

    for name, value in auth_data['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.get(API_URL, headers=auth_data['headers'])

    try:
        response_data = response.json()
        if response_data and 'data' in response_data and response_data['data']:
            user_data = response_data['data'][0]
            print(f"active: {user_data['active']}")
            return f"active: {user_data['active']}"
        else:
            return "No user data found"
    except Exception as e:
        print(f"An error occurred: {e}")
        return None


@pytest.fixture()
def nemo_delete_learner_report_api(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/learner_report/"

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    response = session.delete(API_URL, headers=auth_data['headers'])

    try:
        return response.json()
    except:
        return None


@pytest.fixture()
def nemo_create_learner_report_api(auth_data):
    session = Session()
    API_URL = f"{Urls.HOST}/api/learner_report/"

    send_start_date = datetime.now().strftime("%Y-%m-%d %H:%M")
    send_end_date = (datetime.now() + timedelta(days=365)).strftime("%Y-%m-%d %H:%M")

    for name, value in auth_data['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "id": None,
        "send_freq": 3,
        "send_interval": 1,
        "send_hour": 9,
        "send_week_days": [0],
        "send_month_days": [10],
        "send_start_date": send_start_date,
        "send_end_date": send_end_date,
        "data_range": 1,
        "is_active": True,
        "template_html": "hello world",
        "sender_name": "",
        "email_subject": "Personal learner summary report",
        "test_mailing_list": [],
        "audience": {
            "employees": [],
            "dyn_groups": [],
            "groups": []
        }
    }

    response = session.post(API_URL, json=data, headers=auth_data['headers'])

    try:
        return response.json()['id']
    except:
        return None
