import time
from logging import Logger

import pytest

from base.logger import <PERSON><PERSON><PERSON><PERSON>
from pages.Tenants.customers.playwright_training_reports import PlaywrightPhishingReports
from ....tenants.customer.reporting.navigate_to_reporting import NavigateToReporting


class TestLearnerReportDelete:

    @classmethod
    def setup_class(cls):
        cls.logger: Logger = QALogger("TestLearnerReportDelete").get_logger()

    def test_delete_learner_report(self, nemo_create_learner_report_api, setup):
        training_reports = NavigateToReporting(setup)
        training_reports.choose_dcoya_customer_from_tenant_choosing_table()
        training_reports.go_to_user_customer_reporting_training(self.logger)
        reports = PlaywrightPhishingReports(setup, self.logger)

        # First create a report to ensure there's something to delete
        try:
            print("🔄 Creating a report first to ensure there's something to delete...")
            reports.create_new_learner_report()
            print("✅ Report created successfully")

            # Wait for the UI to stabilize after report creation
            setup.wait_for_load_state('networkidle', timeout=10000)
            time.sleep(2)  # Additional wait for UI stabilization

            # Verify that we're still on the correct page and browser is still active
            try:
                current_url = setup.url
                print(f"📍 Current URL after report creation: {current_url}")

                # Check if page is still responsive
                page_title = setup.title()
                print(f"📄 Page title: {page_title}")

                # Verify browser context is still active
                is_closed = setup.is_closed()
                print(f"🌐 Page closed status: {is_closed}")

                if is_closed:
                    print("❌ ERROR: Browser page has been closed!")
                    raise Exception("Browser page was closed after report creation")

            except Exception as url_check_e:
                print(f"❌ Error checking page status: {url_check_e}")
                raise

            # Check if there are any reports visible in the list
            try:
                report_elements = setup.locator("//tr[contains(@class, 'MuiTableRow')]")
                report_count = report_elements.count()
                print(f"📊 Found {report_count} reports in the list")

                if report_count == 0:
                    print("⚠️ No reports found in list, but creation seemed successful")
                else:
                    print("✅ Reports are visible in the list")

            except Exception as check_e:
                print(f"⚠️ Could not verify reports in list: {check_e}")

            print("✅ UI stabilized, now attempting to delete...")

        except Exception as e:
            print(f"❌ Could not create report first: {e}")
            # Still try to delete in case there are existing reports
            print("🔄 Proceeding with deletion attempt anyway...")

        # Now try to delete the report
        try:
            reports.delete_learner_report()
            print("✅ Learner report deletion completed successfully")

            # Verify deletion was successful
            setup.wait_for_load_state('networkidle', timeout=5000)
            print("✅ Learner report deletion test completed successfully")

        except Exception as delete_e:
            print(f"❌ Deletion failed: {delete_e}")
            raise
