from pages.Tenants.multitenant_properties_page import MultiTenantPropertiesPage
from pages.Tenants.tenant_page import TenantPage


class BaseSelectCustomer:
    """
    This class is the base class for selecting a customer
    """
    def __init__(self, page):
        self.page = page

    def click_multi_tenant_properties_button(self, refresh_page=False):
        """
        This method clicks on the multi tenant properties button
        :param refresh_page: boolean value to refresh the page - helpful when running multiple tests from the same testing class
        :return: None
        """
        if refresh_page:
            self.page.reload()
        tenant_page = TenantPage(self.page)
        tenant_page.click_multi_tenant_properties_button()

    def click_on_selected_customer(self):
        """
        This method clicks on the selected customer
        :return: None
        """
        multitenant_properties_window = MultiTenantPropertiesPage(self.page)
        multitenant_properties_window.select()
