from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait

from ...api_gateway.customer.gateway_data import GatewayData


class BaseLoginGateway:
    def __init__(self, driver):
        self.driver = driver
        self.driver.get(GatewayData.STAGING_URL)
        self.wait = WebDriverWait(self.driver, 50)
        self.username_field = (By.XPATH, "//input[@id='id_auth-username']")
        self.password_field = (By.XPATH, "//input[@id='id_auth-password']")
        self.login_button = (By.XPATH, "//button[normalize-space()='Login']")

    def set_username(self, username):
        user_element = self.wait.until(EC.presence_of_element_located(self.username_field))
        if user_element.is_displayed():
            user_element.send_keys(username)

    def set_password(self, password):
        password_element = self.wait.until(EC.presence_of_element_located(self.password_field))
        if password_element.is_displayed():
            password_element.send_keys(password)

    def click_login(self):
        click_element = self.wait.until(EC.presence_of_element_located(self.login_button))
        if click_element.is_displayed():
            click_element.click()

    def login_to_system(self, username, password):
        self.set_username(username)
        self.set_password(password)
        self.click_login()
