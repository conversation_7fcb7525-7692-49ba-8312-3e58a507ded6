import json


class ResponseValidator:
    @staticmethod
    def assert_response_structure(response, expected_structure, status_code=200):

        assert response.status_code == status_code, f"Expected status code {status_code}, got {response.status_code}"

        response_content = json.loads(response.content)
        print(json.dumps(response_content, indent=2))

        for key, expected in expected_structure.items():
            assert key in response_content, f"Missing key: {key}"

            if isinstance(expected, type):
                assert isinstance(response_content[key], expected), \
                    f"Expected {key} to be of type {expected}, got {type(response_content[key])}"
            else:
                assert response_content[key] == expected, \
                    f"Expected {key} to be {expected}, got {response_content[key]}"


