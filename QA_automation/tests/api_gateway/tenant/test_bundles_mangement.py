import pytest
import requests
import json

from ..tenant.tenant_gateway_data import TenantGatewayData


class TestBundlesManagement:

    @pytest.mark.main
    def test_get_details_of_specific_tenant_bundle(self, get_headers):
        host = TenantGatewayData().HOST

        api = f'https://{host}/api/api_gateway/tenant/bundles/'

        data = {
            "bundle_id": 3
        }
        response = requests.get(api, headers=get_headers, params=data)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)
        return response

    @pytest.mark.main
    def test_get_list_of_current_tenant_bundles(self, get_headers):
        host = TenantGatewayData().HOST

        api = f'https://{host}/api/api_gateway/tenant/bundles/'
        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)
        return response
