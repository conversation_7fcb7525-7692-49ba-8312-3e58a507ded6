from datetime import datetime

import pytest
import requests
import json

from ..tenant.tenant_gateway_data import TenantGatewayData


class TestResellerManagement:

    @pytest.mark.main
    def test_create_new_tenant(self, get_headers):
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/create/'
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_name = f"moshe19_tenant_testing_{timestamp}"

        data = {
            "name": unique_name,
            "country": "Israel",
            "parent_tenant_id": 8,
            "time_zone": "Israel"
        }

        response = requests.post(api, headers=get_headers, json=data)

        assert response.status_code in [200,
                                        201], f"Test failed! Status code: {response.status_code}, Response: {response.text}"

        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

# Negative scenario
    def test_tenant_already_exists(self, get_headers):
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/create/'
        data = {
            "name": "moshe109_tenant_testing",
            "country": "Israel",
            "parent_tenant_id": 8,
            "time_zone": "Israel"
        }
        response = requests.post(api, headers=get_headers, json=data)
        assert response.status_code == 400, f"Test failed! Expected status 400, but got {response.status_code}. Response: {response.text}"
        print("Negative test passed: Tenant already exists and returned status 400 as expected.")
        return response
