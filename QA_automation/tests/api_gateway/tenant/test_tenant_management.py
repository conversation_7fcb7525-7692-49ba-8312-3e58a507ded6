import pytest
import requests
import json
from datetime import datetime

from ..tenant.tenant_gateway_data import TenantGatewayData


class TestTenantManagement:
    @pytest.mark.main
    def test_create_customer_under_current_tenant(self, get_headers):
        tenant_id = self.get_tenant_id(get_headers)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_customer_name = f"moshe81_customer_testing_{timestamp}"

        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/customer/create/'

        data = {
            "name": new_customer_name,
            "country": "Israel",
            "parent_tenant_id": tenant_id
        }

        response = requests.post(api, headers=get_headers, json=data)

        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    # This is the negative scenario

    @pytest.mark.main
    def test_customer_under_tenant_already_exist(self, get_headers):
        tenant_id = self.get_tenant_id(get_headers)
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/customer/create/'
        data = {
            "name": "moshe82_customer_testing",
            "country": "Israel",
            "parent_tenant_id": tenant_id
        }
        response = requests.post(api, headers=get_headers, json=data)
        assert response.status_code == 400, f"Test failed! Expected status 400, but got {response.status_code}.Response: {response.text}"
        print("Negative test passed: Tenant already exists and returned status 400 as expected.")
        return response

    # Fetch direct customers under the current tenant
    @pytest.mark.main
    def test_fetch_current_tenant_direct_customers(self, get_headers):
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/direct_customers/'
        response = requests.get(api, headers=get_headers)

        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    # List all tenants and customers in the hierarchy
    @pytest.mark.main
    def test_List_your_customers_and_tenants(self, get_headers):
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/hierarchy/'
        response = requests.get(api, headers=get_headers)

        print(json.dumps(json.loads(response.content), indent=2))
        return response

    # Helper function to get the tenant ID
    def get_tenant_id(self, get_headers):
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/hierarchy/'
        response = requests.get(api, headers=get_headers)
        response_data = json.loads(response.content)

        tenant_id = response_data[0]['id']
        print(f"id: {tenant_id}")

        return tenant_id

    # Helper function to check if a customer name already exists in the tenant list
    def get_tenant_name(self, get_headers):
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/hierarchy/'
        response = requests.get(api, headers=get_headers)
        response_data = json.loads(response.content)

        customers = response_data[0]['customers']
        customer_names = [customer['name'] for customer in customers]

        return customer_names
