import pytest
import requests
import json

from ..tenant.tenant_gateway_data import TenantGatewayData


class TestCollectorsManagement:

    # issue with this api
    @pytest.mark.main
    def test_retrieve_tenant_collection_data(self, get_headers, collection_id=10):
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/collections/{collection_id}/'

        response = requests.get(api, headers=get_headers)
        assert response.status_code in [200,201], f"Test failed! Status code: {response.status_code}, Response: {response.text}"

        print(json.dumps(json.loads(response.content), indent=2))
        print(response)
        return response

    @pytest.mark.main
    def test_retrieve_tenant_collections(self, get_headers):
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/collections/'
        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)
        return response
