import pytest
import requests
import json

from ..tenant.tenant_gateway_data import TenantGatewayData


class TestMassDelivery:

    @pytest.mark.main
    def test_get_list_of_mass_campaigns_deliveries(self, get_headers):
        host = TenantGatewayData().HOST
        api = f'https://{host}/api/api_gateway/tenant/mass_campaign_delivery/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)
        return response
