from datetime import datetime, timedelta
from uuid import uuid4

import pytest
import requests

from .gateway_data import GatewayData
from ..api_gateway_data.response_validator import ResponseValidator


class TestCampaignsTraining:
    @pytest.fixture(autouse=True)
    def setup(self):
        self.CAMPAIGN_NAME = f"Training Campaign {datetime.now().isoformat()}"
        self.current_date = datetime.now().strftime("%Y-%m-%d")
        self.end_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")

    @pytest.mark.main
    def test_create_new_training_campaign(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/campaigns/training/'
        timestamp = datetime.now().strftime("%B %d, %Y at %I:%M %p")
        base_campaign_name = f"Campaign created on {timestamp} [{uuid4().hex}]"

        request_data = {
            "name": base_campaign_name,
            "start_date": self.current_date,
            "end_date": self.end_date,
            "close_campaign_after_end": True
        }
        response = requests.post(api, headers=get_headers, json=request_data)
        response_data = response.json()
        print(response.json())

        assert response.status_code == 201, f"Expected status code 201, got {response.status_code}"
        assert isinstance(response_data, dict), "Response is not a dictionary"

        assert "id" in response_data, "'id' field is missing from response"
        assert isinstance(response_data["id"], int), "'id' should be an integer"

        assert "name" in response_data, "'name' field is missing from response"
        assert response_data["name"] == request_data["name"], (
            f"Expected 'name' to be '{request_data['name']}', got '{response_data['name']}'"
        )

        return response_data

    @pytest.mark.main
    def test_same_name_training_campaign(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/campaigns/training/'
        duplicate_name = "moshe_duplicate"

        data = {
            "name": duplicate_name,
            "start_date": self.current_date,
            "end_date": self.end_date,
            "close_campaign_after_end": True
        }

        responses = []
        expected_response_structures = [
            {"id": int, "name": str},
            {"error": str}
        ]

        for i in range(2):
            response = requests.post(api, headers=get_headers, json=data)
            responses.append(response)
        if responses[1].status_code == 409:
            ResponseValidator.assert_response_structure(
                responses[1], expected_response_structures[1], status_code=409
            )
        else:
            ResponseValidator.assert_response_structure(
                responses[1], expected_response_structures[0]
            )

    @pytest.mark.main
    def test_get_specific_training_campaign(self, get_headers, get_campaign_id):
        host = GatewayData.HOST
        campaign_id = get_campaign_id
        api = f'https://{host}/api/api_gateway/customer/campaigns/training/{campaign_id}/'

        response = requests.get(api, headers=get_headers)
        response_data = response.json()

        assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
        assert isinstance(response_data, dict), "Response is not a dictionary"

        expected_fields = {
            "campaign_name": str,
            "emotions": list,
            "start_date": str,
            "end_date": str,
            "campaign_id": int,
            "status": int,
            "sending_days": list,
            "simulations_ids": list
        }

        for field, field_type in expected_fields.items():
            assert field in response_data, f"'{field}' is missing in response"
            assert isinstance(response_data[field], field_type), (
                f"'{field}' should be of type {field_type.__name__}, got {type(response_data[field]).__name__}"
            )

    @pytest.fixture
    def get_campaign_id(self, get_headers):
        try:
            created_campaign = self.test_create_new_training_campaign(get_headers)
            campaign_id = created_campaign["id"]
            print(f"Campaign ID: {campaign_id}")
            return campaign_id
        except Exception as e:
            print(f"Failed to create a new training campaign: {e}")
            return None

    @pytest.mark.main
    def test_get_all_training_campaigns(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/campaigns/training/'

        response = requests.get(api, headers=get_headers)
        response_data = response.json()

        assert response.status_code == 200, f"Expected status code 200, got {response.status_code}"
        assert isinstance(response_data, list), "Expected response to be a list of campaigns"
        assert len(response_data) > 0, "Response contains no campaigns"

        for campaign in response_data:
            assert "campaign_id" in campaign and isinstance(campaign["campaign_id"], int), (
                "Each campaign should have an integer 'campaign_id'"
            )
            assert "campaign_name" in campaign and isinstance(campaign["campaign_name"], str), (
                "Each campaign should have a string 'campaign_name'"
            )
