import base64
import os
import pytest
import requests
from .gateway_data import GatewayData
import json


class TestSettings:
    class TestCustomerLogo:

        @pytest.mark.main
        def test_set_customer_logo(self, get_headers):
            host = GatewayData.HOST
            api = f'https://{host}/api/api_gateway/customer/logo/'
            with open(os.path.join(os.path.dirname(__file__), "html_resorces", "system_main_logo.png"), "rb") as f:
                img = f.read()
            img_b64 = base64.b64encode(img).decode()

            data = {
                "image_base64": img_b64
            }
            response = requests.post(api, headers=get_headers, json=data)
            print(json.dumps(json.loads(response.content), indent=2))
            print(response)
            assert response.status_code in [200, 201], f"Unexpected status code: {response.status_code}"
            return response

    @pytest.mark.main
    def test_get_customer_logo(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/logo/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)
        assert response.status_code in [200, 201], f"Unexpected status code: {response.status_code}"

        return response

    @pytest.mark.main
    def test_create_new_user_for_customer(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/users/'
        data = {
            "email": "<EMAIL>",
            "username": "k",
            "permissions": [1],
            "password": "Aa123456!"
        }

        response = requests.post(api, headers=get_headers, json=data)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_delete_customer_user(self, get_headers, user_id=71):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/users/71/'
        response = requests.delete(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_customer_domains(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/domains/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_customer_users(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/users/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_customer_bundles(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/bundles/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response
