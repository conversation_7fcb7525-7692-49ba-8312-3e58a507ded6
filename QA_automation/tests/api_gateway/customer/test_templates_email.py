import pytest
import requests
from .gateway_data import GatewayData
import json


class TestTemplatesTEmail:

    @pytest.mark.main
    def test_get_all_email_templates(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/templates/email/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response
