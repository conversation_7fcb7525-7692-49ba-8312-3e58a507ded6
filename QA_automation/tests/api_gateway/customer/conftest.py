import pytest
from requests import Session

from data.group_names import GroupNames

from datetime import datetime, timedelta

group_name = GroupNames().Group_NAME


@pytest.fixture()
def get_headers():
    headers = {
        'apiKey': 'DC-CU-37-2b31059f10e546be8968671256cfeeaba702d1d28d5249ba9cdaa8dc9191cbc0'

    }
    return headers


@pytest.fixture()
def nemo_create_learner_report_api(auth_data_api):
    session = Session()
    API_URL = "https://dashboard.cyber-pal.online/api/learner_report/"

    send_start_date = datetime.now().strftime("%Y-%m-%d %H:%M")
    send_end_date = (datetime.now() + timedelta(days=365)).strftime("%Y-%m-%d %H:%M")

    for name, value in auth_data_api['cookies'].items():
        if name == 'id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    data = {
        "id": None,
        "send_freq": 3,
        "send_interval": 1,
        "send_hour": 9,
        "send_week_days": [0],
        "send_month_days": [10],
        "send_start_date": send_start_date,
        "send_end_date": send_end_date,
        "data_range": 1,
        "is_active": True,
        "template_html": "hello world",
        "sender_name": "",
        "email_subject": "Personal learner summary report",
        "test_mailing_list": [],
        "audience": {
            "employees": [],
            "dyn_groups": [],
            "groups": []
        }
    }

    response = session.post(API_URL, json=data, headers=auth_data_api['headers'])

    try:
        return response.json()['id']
    except:
        return None


@pytest.fixture()
def get_learner_report_id(auth_data_api, nemo_create_learner_report_api):
    session = Session()
    API_URL = "https://dashboard.cyber-pal.online/api/learner_report/"

    for name, value in auth_data_api['cookies'].items():
        if name == 'customer_id' and value == '-1':
            session.cookies.set(name, '1')
        else:
            session.cookies.set(name, value)

    try:
        response = session.get(API_URL, headers=auth_data_api['headers'])
        response.raise_for_status()
        response_json = response.json()
        report_id = response_json.get('id', )
        print(report_id)
        return report_id


    except:

        return None
