
import requests
from .gateway_data import GatewayData
from datetime import datetime, timedelta
import json


class TestReportsEditLearnerReport:
    current_date = datetime.now().strftime('%Y-%m-%d')
    end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')

    def test_edit_learner_report(self, get_headers,get_learner_report_id):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/learner-report/'

        data = {
            "send_freq": 3,
            "send_interval": 1,
            "send_hour": 9,
            "send_week_days": [1, 2, 3],
            "send_month_days": [1, 15],
            "send_start_date": (datetime.now() - timedelta(days=1)).replace(microsecond=0).isoformat(' ', 'minutes'),
            "send_end_date": (datetime.now() + timedelta(days=360)).replace(microsecond=0).isoformat(' ', 'minutes'),
            "data_range": 10,
            "is_active": True,
            "sender_name": "Test Sender",
            "email_subject": "Test Subject",
            "template_html": "<html><body>Test Template</body></html>"
        }

        response = requests.put(api, headers=get_headers, json=data)
        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        actual_response = response.json()
        print(json.dumps(actual_response, indent=2))

        assert "id" in actual_response and isinstance(actual_response["id"], int), "Expected 'id' to be an integer"
