import pytest
import requests

from .gateway_data import GatewayData

"""
  test_delete_learner_report: This test validates the deletion of a learner report.
Initially, the test uses the `get_learner_report_id` fixture from `conftest.py`.
The `get_learner_report_id` function handles the creation of a new learner report
and retrieves its ID, ensuring that the test has a valid report ID to work with.
This setup avoids test dependencies by guaranteeing a fresh report ID for every test run.
"""


class TestReportsDeleteLearnerReport:

    def test_delete_learner_report(self, get_headers, get_learner_report_id):
        report_id = get_learner_report_id

        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/learner-report/'
        data = {"report_id": report_id}

        response = requests.delete(api, headers=get_headers, json=data)

        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        actual_response = response.json()
        assert "message" in actual_response, "Response does not contain 'message' key"
        assert isinstance(actual_response["message"], str), "Response 'message' should be a string"

        expected_message = "Report deleted successfully"
        assert actual_response["message"] == expected_message, (
            f"Expected response message: '{expected_message}', but got: '{actual_response['message']}'"
        )

        if "does not exist" in actual_response["message"].lower():
            pytest.fail(f"Test failed: {actual_response['message']}")

        return response
