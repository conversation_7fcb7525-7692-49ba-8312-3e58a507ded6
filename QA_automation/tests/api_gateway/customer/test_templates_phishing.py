import pytest
import requests
from .gateway_data import GatewayData
import json


class TestTemplatesPhishing:

    @pytest.mark.main
    def test_get_all_phishing_templates(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/templates/phishing/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_specific_phishing_template(self, get_headers, template_id='15'):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/templates/phishing/{template_id}/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response
