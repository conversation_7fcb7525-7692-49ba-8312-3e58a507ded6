import pytest
import requests
from .gateway_data import GatewayData
import json


class TestTemplatesTraining:

    @pytest.mark.main
    def test_get_all_training_templates(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/templates/training/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_aware_training_data(self, get_headers, template_id='25'):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/templates/training/{template_id}/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_template_lms_data(self, get_headers, template_id='50'):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/templates/training/performance/{template_id}/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_template_statistics(self, get_headers, template_id='9'):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/templates/training/statistics/{template_id}/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

