import pytest
import requests
from datetime import datetime, timedelta
from .gateway_data import GatewayData
import uuid
import json


class TestSimulationTraining:
    current_date = datetime.now().strftime('%Y-%m-%d')
    end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')

    # def test_create_new_simulation_training(self, get_headers):
    #     host = GatewayData.HOST
    #     api = f'https://{host}/api/api_gateway/customer/simulations/training/'
    #     current_date = datetime.now().strftime('%Y-%m-%d')
    #     end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')
    #
    #     data = {
    #         "name": "abcde",
    #         "start_date": current_date,
    #         "end_date": end_date,
    #         "weekdays": ["mon", "tue", "wed", "thu", "fri"],
    #         "time_start": "22:05:00",
    #         "time_end": "09:45:00",
    #         "email_template_id": 18,
    #         "training_template_id": 25,
    #         "target_employees": [4494],
    #         "target_groups": [],
    #         "target_dyn_groups": [],
    #         "ssl": True,
    #         "schedule_and_start": False
    #     }
    #
    #     response = requests.post(api, headers=get_headers, json=data)
    #     print(json.dumps(json.loads(response.content), indent=2))
    #     print(response)
    #
    #     return response

    @pytest.mark.main
    def test_get_specific_training_simulation(self, get_headers, simulation_id='537'):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/simulations/training/{simulation_id}/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_all_training_simulations(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/simulations/training/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response
