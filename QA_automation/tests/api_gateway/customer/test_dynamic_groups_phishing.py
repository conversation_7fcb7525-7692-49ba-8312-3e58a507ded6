import pytest
import requests
from .gateway_data import GatewayData
import json


class TestDynamicGroupsPhishing:

    @pytest.mark.main
    def test_get_all_phishing_dynamic_groups(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/groups/dynamic/phishing/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_specific_phishing_dynamic_group(self, get_headers, group_id='64'):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/groups/dynamic/phishing/{group_id}/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response
