import pytest
import requests
from .gateway_data import GatewayData
import json


class TestSimulationPhishing:

    @pytest.mark.main
    def test_get_all_phishing_simulations(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/simulations/phishing/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response

    @pytest.mark.main
    def test_get_single_phishing_simulation(self, get_headers, simulation_id='535'):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/simulations/phishing/{simulation_id}/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        return response
