import pytest
import requests
from .gateway_data import GatewayData
from datetime import datetime, timedelta
import json


class TestGetLearnerReport:
    current_date = datetime.now().strftime('%Y-%m-%d')
    end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')

    def test_get_learner_report(self, get_learner_report_id, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/learner-report/'

        response = requests.get(api, headers=get_headers)
        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        actual_response = response.json()
        print(json.dumps(actual_response, indent=2))

        assert "id" in actual_response and isinstance(actual_response["id"], int), "Expected 'id' to be an integer"
        assert "send_freq" in actual_response and isinstance(actual_response["send_freq"],
                                                             int), "Expected 'send_freq' to be an integer"
        assert "send_interval" in actual_response and isinstance(actual_response["send_interval"],
                                                                 int), "Expected 'send_interval' to be an integer"
        assert "send_hour" in actual_response and isinstance(actual_response["send_hour"],
                                                             int), "Expected 'send_hour' to be an integer"
        assert 0 <= actual_response["send_hour"] <= 23, f"'send_hour' out of range: {actual_response['send_hour']}"

        assert "send_week_days" in actual_response and isinstance(actual_response["send_week_days"],
                                                                  list), "Expected 'send_week_days' to be a list"
        for day in actual_response["send_week_days"]:
            assert isinstance(day, int) and 0 <= day <= 7, f"Invalid 'send_week_days' entry: {day}"

        assert "send_month_days" in actual_response and isinstance(actual_response["send_month_days"],
                                                                   list), "Expected 'send_month_days' to be a list"
        for day in actual_response["send_month_days"]:
            assert isinstance(day, int) and 0 <= day <= 31, f"Invalid 'send_month_days' entry: {day}"

        assert "send_start_date" in actual_response and isinstance(actual_response["send_start_date"],
                                                                   str), "Expected 'send_start_date' to be a string"
        assert "send_end_date" in actual_response and isinstance(actual_response["send_end_date"],
                                                                 str), "Expected 'send_end_date' to be a string"

        try:
            start_date = datetime.strptime(actual_response["send_start_date"], "%Y-%m-%d %H:%M")
            end_date = datetime.strptime(actual_response["send_end_date"], "%Y-%m-%d %H:%M")
            assert start_date < end_date, "Expected 'send_start_date' to be earlier than 'send_end_date'"
        except ValueError:
            pytest.fail(
                "Date format in 'send_start_date' or 'send_end_date' is incorrect, expected format: 'YYYY-MM-DD HH:MM'")

        assert "data_range" in actual_response and isinstance(actual_response["data_range"],
                                                              int), "Expected 'data_range' to be an integer"
        assert "is_active" in actual_response and isinstance(actual_response["is_active"],
                                                             bool), "Expected 'is_active' to be a boolean"
        assert "template_html" in actual_response and isinstance(actual_response["template_html"],
                                                                 str), "Expected 'template_html' to be a string"
        assert actual_response["template_html"].startswith("<html>") and actual_response["template_html"].endswith(
            "</html>"), "Expected 'template_html' to contain HTML content"
        assert "sender_name" in actual_response and isinstance(actual_response["sender_name"],
                                                               str), "Expected 'sender_name' to be a string"
        assert "email_subject" in actual_response and isinstance(actual_response["email_subject"],
                                                                 str), "Expected 'email_subject' to be a string"

        assert "test_mailing_list" in actual_response and isinstance(actual_response["test_mailing_list"],
                                                                     list), "Expected 'test_mailing_list' to be a list"
        assert "audience" in actual_response and isinstance(actual_response["audience"],
                                                            dict), "Expected 'audience' to be a dictionary"
