from uuid import uuid4

import pytest
import requests
import json
from .gateway_data import GatewayData


class TestUserManagement:
    @pytest.mark.main
    def test_get_all_customer_groups(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/groups/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        response_data = response.json()
        assert response_data, "Response data is empty"

        for group in response_data:

            assert isinstance(group.get('id'), int), f"'id' should be an integer, got {type(group.get('id'))}"
            assert isinstance(group.get('name'), str), f"'name' should be a string, got {type(group.get('name'))}"

            if 'tags' in group and group['tags']:
                for tag in group['tags']:
                    assert isinstance(tag.get('tag_id'),
                                      int), f"'tag_id' should be an integer, got {type(tag.get('tag_id'))}"
                    assert isinstance(tag.get('tag_name'),
                                      str), f"'tag_name' should be a string, got {type(tag.get('tag_name'))}"
            else:
                assert group.get('tags', []) == [], "Tags should be an empty list when not present"

        return response

    @pytest.mark.main
    def test_get_all_group_tags_of_customer(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/groups/tags/'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        response_data = response.json()
        assert isinstance(response_data, list), "Expected response to be a list"

        for tag in response_data:
            assert 'tag_id' in tag, "Missing 'tag_id' in response item"
            assert isinstance(tag['tag_id'], int), f"'tag_id' should be an integer, got {type(tag['tag_id'])}"

            assert 'tag_name' in tag, "Missing 'tag_name' in response item"
            assert isinstance(tag['tag_name'], str), f"'tag_name' should be a string, got {type(tag['tag_name'])}"

        return response

    @pytest.mark.main
    def test_edit_customer_group(self, get_headers, group_id=260):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/groups/{group_id}/'
        data = {
            "group_name": "moshe_abcd",
            "employee_ids": [7017]
        }

        response = requests.put(api, headers=get_headers, json=data)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        response_data = response.json()
        assert response_data, "Response data is empty"

        assert 'id' in response_data, "Missing 'id' in response"
        assert isinstance(response_data['id'], int), f"'id' should be an integer, got {type(response_data['id'])}"

        assert 'name' in response_data, "Missing 'name' in response"
        assert isinstance(response_data['name'], str), f"'name' should be a string, got {type(response_data['name'])}"

        assert 'employee_count' in response_data, "Missing 'employee_count' in response"
        assert isinstance(response_data['employee_count'],
                          int), f"'employee_count' should be an integer, got {type(response_data['employee_count'])}"

        expected_fields = {'id', 'name', 'employee_count'}
        actual_fields = set(response_data.keys())
        unexpected_fields = actual_fields - expected_fields
        assert not unexpected_fields, f"Unexpected fields found in response: {unexpected_fields}"

        return response

    @pytest.mark.main
    def test_create_customer_group(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/groups/'
        unique_group_name = f"group_test_{uuid4().hex[:8]}"

        data = {
            "name": unique_group_name,
            "employee_ids": [7017]
        }

        response = requests.post(api, headers=get_headers, json=data)
        response_content = json.loads(response.content.decode('utf-8'))

        print(json.dumps(response_content, indent=2))
        print(f"Status Code: {response.status_code}")

        assert response.status_code == 201, f"Expected status code 201, but got {response.status_code}"

        assert isinstance(response_content, dict), "Expected response to be a dictionary"
        assert 'id' in response_content, "Response is missing 'id' field"
        assert isinstance(response_content['id'], int), f"'id' should be an integer, got {type(response_content['id'])}"

        assert 'name' in response_content, "Response is missing 'name' field"
        assert response_content[
                   'name'] == unique_group_name, f"Expected group name '{unique_group_name}', got {response_content['name']}"

        assert 'employee_count' in response_content, "Response is missing 'employee_count' field"
        assert isinstance(response_content['employee_count'],
                          int), f"'employee_count' should be an integer, got {type(response_content['employee_count'])}"
        assert response_content['employee_count'] == len(data[
                                                             'employee_ids']), f"Expected employee count {len(data['employee_ids'])}, got {response_content['employee_count']}"

        if response.status_code != 201:
            try:
                error_content = json.loads(response.content.decode('utf-8'))
                error_message = error_content.get('message', 'No error message provided')
                print(f"Error: {error_message}")
            except json.JSONDecodeError:
                print("Error: Unable to decode JSON response")
                print(response.content)

    # Negative test scenario
    @pytest.mark.main
    def test_customer_group_already_exists(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/groups/'
        data = {
            "name": "group_test_aaa",
            "employee_ids": [4467]
        }

        response = requests.post(api, headers=get_headers, json=data)
        response_content = json.loads(response.content.decode('utf-8'))

        print(json.dumps(response_content, indent=2))
        print(f"Status Code: {response.status_code}")

        assert response.status_code == 400, f"Expected status code 400, but got {response.status_code}"

        assert isinstance(response_content, dict), "Expected response to be a dictionary"

        assert 'error' in response_content, "Response is missing 'error' field"
        assert isinstance(response_content['error'],
                          str), f"'error' should be a string, got {type(response_content['error'])}"
        assert response_content['error'].lower() == "group name already exist", (
            f"Expected error message 'group name already exist', got '{response_content['error']}'"
        )

    @pytest.mark.main
    def test_create_new_employee(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/employees/'
        unique_email = f'test_QA_{uuid4().hex[:8]}'
        employee_email = f'{unique_email}@dcoya.com'

        data = {
            "first_name": GatewayData().NEW_EMPLOYEE_NAME,
            "last_name": GatewayData().NEW_EMPLOYEE_LAST_NAME,
            "email": employee_email,
            "country": "t",
            "department": "g",
            "phone": "555",
            "manager": "h",
            "job_title": "d",
            "branch": "c",
            "active": "true",
            "preferred_language": "1"
        }

        response = requests.post(api, headers=get_headers, json=data)
        response_data = json.loads(response.content.decode('utf-8'))
        print(json.dumps(response_data, indent=2))
        print(response)

        assert response.status_code == 201, f"Expected status code 201, but got {response.status_code}"
        assert isinstance(response_data, dict), "Expected response to be a dictionary"
        assert 'id' in response_data, "Missing 'id' in response"
        assert isinstance(response_data['id'], int), f"'id' should be an integer, got {type(response_data['id'])}"
        assert 'email' in response_data, "Missing 'email' in response"
        assert response_data[
                   'email'] == employee_email, f"Expected email to match {employee_email}, got {response_data['email']}"
        assert isinstance(response_data['email'],
                          str), f"'email' should be a string, got {type(response_data['email'])}"

    # Negative test scenario

    @pytest.mark.main
    def test_employee_already_exists(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/employees/'

        data = {
            "customer_id": 73,
            "first_name": GatewayData().NEW_EMPLOYEE_NAME,
            "last_name": GatewayData().NEW_EMPLOYEE_LAST_NAME,
            "email": GatewayData().NEW_EMPLOYEE_EMAIL,
            "country": "t",
            "department": "g",
            "phone": "555",
            "manager": "h",
            "job_title": "d",
            "branch": "c",
            "active": "true"
        }

        response = requests.post(api, headers=get_headers, json=data)
        print(json.dumps(response.json(), indent=2))
        print(response)

        assert response.status_code == 400, f"Expected status code 400, but got {response.status_code}"

        response_data = response.json()

        assert "error" in response_data, "Expected 'error' key in response"
        assert isinstance(response_data["error"],
                          str), f"Expected 'error' to be a string, got {type(response_data['error'])}"

        expected_error_message = "Invalid Json Payload."
        assert response_data[
                   "error"] == expected_error_message, f"Expected '{expected_error_message}', but got '{response_data['error']}'"

        error_details = response_data.get("error_details", [])
        assert any(detail.get("error_location") == "customer_id" for detail in
                   error_details), "Expected 'customer_id' in error details"

        return response

    @pytest.mark.main
    def test_edit_customer_employee(self, get_headers, employee_id=7017):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/employees/{employee_id}/'
        data = {
            "first_name": "M",
            "last_name": "A",
            "email": GatewayData().NEW_EMPLOYEE_EMAIL,
            "country": "e",
            "department": "f",
            "phone": "444",
            "manager": "j",
            "job_title": "n",
            "branch": "ds",
            "active": "true"
        }
        response = requests.put(api, headers=get_headers, json=data)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)
        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"
        response_data = response.json()
        assert isinstance(response_data, dict), "Expected response to be a dictionary"

        assert 'id' in response_data, "Missing 'id' in response"
        assert isinstance(response_data['id'], int), f"'id' should be an integer, got {type(response_data['id'])}"

        assert 'email' in response_data, "Missing 'email' in response"
        assert isinstance(response_data['email'],
                          str), f"'email' should be a string, got {type(response_data['email'])}"

        return response

    @pytest.mark.main
    def test_get_customer_employees_id(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/employees/?employee_ids=4513'

        response = requests.get(api, headers=get_headers)
        print(json.dumps(json.loads(response.content), indent=2))
        print(response)

        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        response_data = response.json()
        assert isinstance(response_data, dict), "Expected response to be a dictionary"

        assert 'employees' in response_data, "Missing 'employees' in response"
        employees = response_data['employees']
        assert isinstance(employees, list), "Expected 'employees' to be a list"

        for employee in employees:
            assert 'id' in employee, "Missing 'id' in employee data"
            assert isinstance(employee['id'], int), f"'id' should be an integer, got {type(employee['id'])}"

            assert 'first_name' in employee, "Missing 'first_name' in employee data"
            assert isinstance(employee['first_name'],
                              str), f"'first_name' should be a string, got {type(employee['first_name'])}"

            assert 'last_name' in employee, "Missing 'last_name' in employee data"
            assert isinstance(employee['last_name'],
                              str), f"'last_name' should be a string, got {type(employee['last_name'])}"

            assert 'email' in employee, "Missing 'email' in employee data"
            assert isinstance(employee['email'], str), f"'email' should be a string, got {type(employee['email'])}"

            optional_fields = {
                'country': str,
                'department': str,
                'phone': str,
                'manager': str,
                'job_title': str,
                'branch': str,
                'created_date': str,
                'active': bool,
                'level': int,
                'lured_in_curiosity': int,
                'lured_in_greed': int,
                'lured_in_fear': int,
                'lured_in_urgency': int,
                'lured_in_obedience': int,
                'simulations_participated': int,
                'simulations_lured': int,
                'trainings_completed': int,
                'trainings_not_started': int
            }

            for field, expected_type in optional_fields.items():
                if field in employee:
                    assert isinstance(employee[field],
                                      expected_type), f"'{field}' should be of type {expected_type}, got {type(employee[field])}"

        assert 'total_items' in response_data, "Missing 'total_items' in response"
        assert isinstance(response_data['total_items'],
                          int), f"'total_items' should be an integer, got {type(response_data['total_items'])}"

        return response

    @pytest.mark.main
    def test_get_customer_employees(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/employees/'

        response = requests.get(api, headers=get_headers)
        response_data = response.json()

        expected_fields = ["id", "first_name", "last_name", "email", "country", "department", "phone", "manager",
                           "job_title", "branch", "created_date", "active"]

        expected_types = {
            "id": int,
            "first_name": str,
            "last_name": str,
            "email": str,
            "country": str,
            "department": str,
            "phone": str,
            "manager": str,
            "job_title": str,
            "branch": str,
            "created_date": str,
            "active": bool
        }

        simplified_employees = [
            {field: employee[field] for field in expected_fields if field in employee}
            for employee in response_data.get("employees", [])
        ]

        for employee in simplified_employees:
            for field in expected_fields:
                if field in employee:
                    assert isinstance(employee[field], expected_types[
                        field]), f"Field '{field}' has incorrect type for employee: {employee}"

        simplified_response = {
            "employees": simplified_employees,
            "page": response_data.get("page"),
            "total_pages": response_data.get("total_pages"),
            "total_items": response_data.get("total_items")
        }

        print(json.dumps(simplified_response, indent=2))
        return simplified_response
