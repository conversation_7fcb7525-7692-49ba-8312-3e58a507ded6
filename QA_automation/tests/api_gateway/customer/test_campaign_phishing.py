import pytest
import requests
from datetime import datetime, timedelta
from .gateway_data import GatewayData
import json
from uuid import uuid4
from ..api_gateway_data.response_validator import ResponseValidator


class TestCampaignPhishing:
    current_date = datetime.now().strftime('%Y-%m-%d')
    end_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')

    @pytest.mark.main
    def test_create_new_phishing_campaign(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/campaigns/phishing/'
        timestamp = datetime.now().strftime("%B %d, %Y at %I:%M %p")
        base_campaign_name = f"Campaign created on {timestamp} [{uuid4().hex}]"
        data = {
            "name": base_campaign_name,
            "start_date": self.current_date,
            "end_date": self.end_date,
            "close_campaign_after_end": True
        }

        response = requests.post(api, headers=get_headers, json=data)

        assert response.status_code == 201

        actual_response = response.json()
        assert "id" in actual_response, "Response does not contain 'id'"
        assert isinstance(actual_response["id"], int), "Response 'id' should be an integer"
        assert "name" in actual_response, "Response does not contain 'name'"
        assert isinstance(actual_response["name"], str), "Response 'name' should be a string"
        assert actual_response["name"] == base_campaign_name, (
            f"Expected campaign name: {base_campaign_name}, but got: {actual_response['name']}"
        )
        return response

    # Negative scenario
    @pytest.mark.main
    def test_new_phishing_campaign_exists_name(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/campaigns/phishing/'
        timestamp = datetime.now().strftime("%B %d, %Y at %I:%M %p")
        campaign_name_date = f"{GatewayData().CAMPAIGN_NAME} {timestamp}"

        data = {
            "name": campaign_name_date,
            "start_date": self.current_date,
            "end_date": self.end_date,
            "close_campaign_after_end": True
        }
        responses = []
        expected_response_structures = [
            {"id": [int], "name": str},
            {"error": str}
        ]

        for i in range(2):
            response = requests.post(api, headers=get_headers, json=data)
            responses.append(response)

        if responses[1].status_code == 409:

            ResponseValidator.assert_response_structure(
                responses[1], expected_response_structures[1], status_code=409
            )
        else:
            ResponseValidator.assert_response_structure(
                responses[1], expected_response_structures[0]
            )

    @pytest.mark.main
    def test_get_single_campaign_id(self, get_headers, get_campaign_id):
        host = GatewayData.HOST
        campaign_id = get_campaign_id
        api = f'https://{host}/api/api_gateway/customer/campaigns/phishing/{campaign_id}/'

        response = requests.get(api, headers=get_headers)
        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        expected_response_structures = {
            "campaign_name": str,
            "start_date": str,
            "end_date": str,
            "campaign_id": int,
            "status": int,
            "sending_days": list,
            "simulations_ids": list
        }

        ResponseValidator.assert_response_structure(response, expected_response_structures, status_code=200)

        actual_response = response.json()

        assert "campaign_name" in actual_response, "Response does not contain 'campaign_name'"
        assert isinstance(actual_response["campaign_name"], str), "Response 'campaign_name' should be a string"

        assert "start_date" in actual_response, "Response does not contain 'start_date'"
        assert isinstance(actual_response["start_date"], str), "Response 'start_date' should be a string"

        assert "end_date" in actual_response, "Response does not contain 'end_date'"
        assert isinstance(actual_response["end_date"], str), "Response 'end_date' should be a string"
        start_date = datetime.fromisoformat(actual_response["start_date"])
        end_date = datetime.fromisoformat(actual_response["end_date"])
        assert end_date > start_date, "Expected 'end_date' to be after 'start_date'"

        assert "campaign_id" in actual_response, "Response does not contain 'campaign_id'"
        assert isinstance(actual_response["campaign_id"], int), "Response 'campaign_id' should be an integer"
        assert actual_response["campaign_id"] == campaign_id, (
            f"Expected campaign_id: {campaign_id}, but got: {actual_response['campaign_id']}"
        )

        assert "status" in actual_response, "Response does not contain 'status'"
        assert isinstance(actual_response["status"], int), "Response 'status' should be an integer"

        assert "sending_days" in actual_response, "Response does not contain 'sending_days'"
        assert isinstance(actual_response["sending_days"], list), "Response 'sending_days' should be a list"

        assert "simulations_ids" in actual_response, "Response does not contain 'simulations_ids'"
        assert isinstance(actual_response["simulations_ids"], list), "Response 'simulations_ids' should be a list"

        assert len(actual_response["sending_days"]) >= 0, "'sending_days' list should not be None"
        assert all(isinstance(day, int) for day in actual_response["sending_days"]), (
            "'sending_days' should contain integers only"
        )

        assert len(actual_response["simulations_ids"]) >= 0, "'simulations_ids' list should not be None"
        assert all(isinstance(sim_id, int) for sim_id in actual_response["simulations_ids"]), (
            "'simulations_ids' should contain integers only"
        )

        return response

    # Help function

    @pytest.fixture
    def get_campaign_id(self, get_headers):
        create_campaign = self.test_create_new_phishing_campaign(get_headers)

        if isinstance(create_campaign, requests.Response):
            response_json = create_campaign.json()
            campaign_id = response_json.get("id")
            print(f"Campaign ID: {campaign_id}")
            return campaign_id
        else:
            print("Failed to create a new phishing campaign. No valid response received.")
            return None

    @pytest.mark.main
    def test_get_all_campaigns(self, get_headers):
        host = GatewayData.HOST
        api = f'https://{host}/api/api_gateway/customer/campaigns/phishing/'

        response = requests.get(api, headers=get_headers)
        assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}"

        response_content = json.loads(response.content)
        assert response_content, "Response content is empty."

        expected_campaign_structure = {
            "campaign_id": int,
            "campaign_name": str,
            "start_date": str,
            "end_date": str,
            "status": int,
            "sending_days": list
        }

        for campaign in response_content:

            for key, expected_type in expected_campaign_structure.items():
                assert key in campaign, f"Key '{key}' is missing in the campaign data"
                assert isinstance(campaign[key],
                                  expected_type), f"Key '{key}' should be of type {expected_type.__name__}, but got {type(campaign[key]).__name__}"

            assert campaign["campaign_id"] > 0, "Campaign 'campaign_id' should be a positive integer"

            assert campaign["campaign_name"], "Campaign 'campaign_name' should not be empty"

            start_date = datetime.fromisoformat(campaign["start_date"])
            end_date = datetime.fromisoformat(campaign["end_date"])

            assert end_date > start_date, (
                f"Expected 'end_date' to be after 'start_date' for campaign '{campaign['campaign_name']}'"
            )

            assert campaign["status"] >= 0, "Campaign 'status' should be a non-negative integer"

            assert isinstance(campaign["sending_days"], list), "Campaign 'sending_days' should be a list"

        print(json.dumps(response_content, indent=2))

        return response
