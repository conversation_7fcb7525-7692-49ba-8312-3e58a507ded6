import pytest
import os
from playwright.sync_api import <PERSON><PERSON>, sync_playwright
from data.urls import Urls
from data.users import Users
from pages.Tenants.login import Login
from pages.download_utility import DownloadUtility

try:
    from playwright.config import get_config
except ImportError:
    # Fallback se o arquivo de configuração não existir
    def get_config():
        return {
            "base_url": os.getenv("BASE_URL", "https://admin.goninjio.com"),
            "viewport": {"width": 1920, "height": 1080},
            "ignore_https_errors": True
        }


@pytest.fixture(scope="module")
def driver():
    chrome_options = Options()
    chrome_options.add_experimental_option("detach", True)
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_argument("--incognito")
    chrome_options.add_argument("disable-infobars")
    download_util = DownloadUtility.get_download_directory()
    chrome_options.add_experimental_option("prefs", {
        "download.default_directory": download_util,
        "download.prompt_for_download": False,  # Disable prompt to avoid download confirmation
    })

    headless = os.getenv("HEADLESS", "False").lower() in ["true", "1", "yes"]
    if headless:
        chrome_options.add_argument("--headless")

    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_cdp_cmd("Page.setDownloadBehavior", {
        "behavior": "allow",
        "downloadPath": download_util
    })
    driver.maximize_window()
    yield driver


@pytest.fixture(scope="module")
def playwright_instance():
    with sync_playwright() as p:
        yield p


@pytest.fixture(scope="module")
def setup(request, playwright_instance):
    headless_str = os.getenv("HEADLESS", "False")
    headless = headless_str.strip().lower() in ["true", "1", "yes"]

    browser = playwright_instance.chromium.launch(
        headless=headless,
        args=[
            "--start-fullscreen",
            "--start-maximized",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--kiosk"
        ],
        ignore_default_args=["--enable-automation"]
    )

    # Create context without viewport to disable device emulation
    context = browser.new_context(no_viewport=True)
    page = context.new_page()

    # Force fullscreen if still not working
    if not headless:
        page.evaluate("() => { document.documentElement.requestFullscreen(); }")

    page.goto(Urls.LOGIN_URL)

    login_page = Login(page)
    login_page.login_to_system(Users.TENANT_USER, Users.TENANT_PASSWORD)
    page.wait_for_load_state('networkidle')

    def teardown():
        context.close()
        browser.close()

    request.addfinalizer(teardown)
    return page


@pytest.fixture(scope="module")
def driver_headless(request, playwright_instance):
    browser = playwright_instance.chromium.launch(
        headless=True,
        args=[
            "--disable-gpu",
            "--no-sandbox",
            "--disable-dev-shm-usage"
        ]
    )
    context = browser.new_context()
    page = context.new_page()

    def teardown():
        context.close()
        browser.close()

    request.addfinalizer(teardown)
    return page


@pytest.fixture(scope="module")
def auth_data(driver_headless):
    page = driver_headless
    context = page.context

    page.goto(Urls.LOGIN_URL)
    login_page = Login(page)
    login_page.login_to_system(Users.CUSTOMER_USER, Users.CUSTOMER_PASSWORD)
    page.wait_for_load_state('networkidle')
    page.wait_for_load_state('load')

    cookies = {cookie['name']: cookie['value'] for cookie in context.cookies()}
    headers = {
        "X-CSRFToken": cookies.get("csrftoken", ""),
        "Content-Type": "application/json"
    }

    return {
        'cookies': cookies,
        'headers': headers
    }


# ============================================================================
# PLAYWRIGHT FIXTURES
# ============================================================================

@pytest.fixture(scope="session")
def playwright_config():
    """Configuração do Playwright."""
    config = get_config()
    return config


@pytest.fixture(scope="session")
def browser_context_args(playwright_config):
    """Argumentos para o contexto do browser."""
    return {
        "viewport": playwright_config.get("viewport", {"width": 1920, "height": 1080}),
        "ignore_https_errors": playwright_config.get("ignore_https_errors", True),
        "base_url": playwright_config.get("base_url", os.getenv("BASE_URL", "https://admin.goninjio.com"))
    }


@pytest.fixture(scope="function")
def playwright_page(page):
    """Fixture para página do Playwright com configurações personalizadas."""
    # Configurar timeout padrão
    page.set_default_timeout(30000)
    page.set_default_navigation_timeout(30000)

    # Ir para a página base se não estiver lá
    if not page.url or page.url == "about:blank":
        page.goto("/")

    yield page


@pytest.fixture(scope="function")
def authenticated_page(playwright_page):
    """Fixture para página autenticada do Playwright."""
    # Fazer login se necessário
    if "/login" in playwright_page.url or "login" in playwright_page.url:
        # Implementar lógica de login aqui se necessário
        pass

    yield playwright_page
