import pytest
from playwright.sync_api import <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext

@pytest.fixture(scope="function")
def browser_context_args(browser_context_args):
    """Override browser context args for all tests."""
    return {
        **browser_context_args,
        "viewport": {
            "width": 1920,
            "height": 1080,
        },
        "ignore_https_errors": True,
    }

@pytest.fixture(scope="function")
def page(browser: Browser) -> Page:
    """Create a new page for each test."""
    context = browser.new_context()
    page = context.new_page()
    yield page
    context.close()