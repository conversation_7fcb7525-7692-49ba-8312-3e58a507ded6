import re
import pytest
from datetime import datetime
from playwright.sync_api import Page, expect
from pytest_bdd import scenarios, given, when, then
from QA_automation.tests.frontend.utils.popup_handlers import handle_survey_popup, dismiss_popups

# Import the feature file
scenarios('features/prod_create_api_key.feature')

# Global variable to store the API key name
api_key_name = ""

# Step definitions for login
@given("I navigate to the production login page")
def navigate_to_login_page(page: Page):
    """Navigate to the production login page."""
    page.goto('https://admin.goninjio.com/login')

@when("I enter valid credentials")
def enter_credentials(page: Page):
    """Enter valid login credentials."""
    page.get_by_role('textbox', name='Username').click()
    page.get_by_role('textbox', name='Username').fill('autotest')
    page.get_by_role('textbox', name='Password').click()
    page.get_by_role('textbox', name='Password').fill('@autotest123')

@when("I click the login button")
def click_login_button(page: Page):
    """Click the login button."""
    page.get_by_role('button', name='Login').click()

@then("I should be logged in successfully")
def verify_login_success(page: Page):
    """Verify successful login."""
    # Wait for navigation to complete after successful login
    page.goto('https://admin.goninjio.com/react/aware')
    expect(page).to_have_url(re.compile(r".*/react/.*"))
    print("Login to production successful!")

@then("I should dismiss the NPS survey popup")
def dismiss_survey_popup(page: Page):
    """Dismiss the NPS survey popup that appears after login."""
    # Handle the NPS survey popup that appears after login
    handle_survey_popup(page)
    print("NPS survey popup dismissed")

# Step definitions for API key management
@when("I navigate to the Integration section")
def navigate_to_integration(page: Page):
    """Navigate to the Integration section."""
    page.get_by_role("button", name=" Integration").click()
    expect(page.get_by_role("button", name="+ New API Key")).to_be_visible()
    print("Navigated to Integration section")

@when("I click on New API Key")
def click_new_api_key(page: Page):
    """Click on New API Key button."""
    page.get_by_role("button", name="+ New API Key").click()
    print("Clicked on New API Key button")

@when("I fill in the API key details with a unique name")
def fill_api_key_details(page: Page):
    """Fill in the API key details with a unique name."""
    global api_key_name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    api_key_name = f"AutoTestAPI_{timestamp}"

    page.get_by_role("textbox", name="API KEY NAME").click()
    page.get_by_role("textbox", name="API KEY NAME").fill(api_key_name)
    print(f"Filled API key name: {api_key_name}")

@when("I set AWARE & SENSE permissions")
def set_aware_sense_permissions(page: Page):
    """Set AWARE & SENSE permissions."""
    # Set AWARE & SENSE Permissions
    page.get_by_role("button", name="AWARE & SENSE Permissions: No").click()

    # Configure Training Campaigns permissions
    page.locator("[id=\"permission-root-Training\\ Campaigns-0_toggleDropdownButton_iconButton\"]").click()
    page.get_by_title("Read only").locator("li").click()

    # Configure Training Dynamic Groups permissions
    page.locator("[id=\"permission-root-Training\\ Dynamic\\ Groups-1_toggleDropdownButton_iconButton\"]").click()
    page.get_by_text("Read only").click()

    # Configure Training Simulations permissions
    page.locator("[id=\"permission-root-Training\\ Simulations-2_toggleDropdownButton_iconButton\"]").click()
    page.get_by_text("Read & write").click()

    # Configure Training Templates permissions
    page.locator("[id=\"permission-root-Training\\ Templates-3_toggleDropdownButton_iconButton\"]").click()
    page.get_by_text("Read only").click()

    # Configure PHISH3D Permissions
    page.locator("#permissions-1_toggleDropdownButton_iconButton").click()
    try:
        page.locator("div").filter(has_text=re.compile(r"^PHISH3DPermissions:No accessRead onlyRead & write$")).first.click()
    except:
        # Alternative selector if the first one fails
        page.get_by_text("No access").click()

    print("Set AWARE & SENSE permissions")

@when("I set Customer permissions")
def set_customer_permissions(page: Page):
    """Set Customer permissions."""
    # Set Customer Permissions
    page.get_by_role("button", name="Customer Permissions: No").click()

    # Configure Phishing Campaigns permissions
    page.locator("[id=\"permission-root-Phishing\\ Campaigns-0_toggleDropdownButton_iconButton\"]").click()
    page.get_by_text("Read & write").click()

    # Configure Phishing Dynamic Groups permissions
    page.locator("[id=\"permission-root-Phishing\\ Dynamic\\ Groups-1_toggleDropdownButton_iconButton\"]").click()
    page.get_by_title("Read only").locator("li").click()

    # Configure Phishing Simulations permissions
    page.locator("[id=\"permission-root-Phishing\\ Simulations-2_toggleDropdownButton_iconButton\"]").click()
    page.get_by_text("Read only").click()

    # Configure Phishing Templates permissions
    page.locator("[id=\"permission-root-Phishing\\ Templates-3_toggleDropdownButton_iconButton\"]").click()
    page.get_by_title("Read only").locator("li").click()

    # Configure Bundles permissions
    page.locator("#permission-root-Bundles-0_toggleDropdownButton_iconButton").click()
    page.get_by_text("Read only").click()

    # Configure Domains permissions
    page.locator("#permission-root-Domains-1_toggleDropdownButton_iconButton").click()
    page.get_by_text("Read only").click()

    # Configure Employees permissions
    page.locator("#permission-root-Employees-2_toggleDropdownButton_iconButton").click()
    page.get_by_text("Read only").click()

    # Configure Groups permissions
    page.locator("#permission-root-Groups-3_toggleDropdownButton_iconButton").click()
    page.get_by_text("Read only").click()

    # Configure Group Tags permissions
    page.locator("[id=\"permission-sub-Groups-Group\\ Tags-3-0_toggleDropdownButton_iconButton\"]").click()
    page.get_by_text("Read only").click()

    # Configure Logos permissions
    page.locator("#permission-root-Logos-4_toggleDropdownButton_iconButton").click()
    page.get_by_text("Read only").click()

    # Configure Users permissions
    page.locator("#permission-root-Users-5_toggleDropdownButton_iconButton").click()
    page.get_by_title("Read only").locator("li").click()

    print("Set Customer permissions")

@when("I click Generate key")
def click_generate_key(page: Page):
    """Click Generate key button."""
    page.get_by_role("button", name="Generate key").click()
    print("Clicked Generate key button")

@then("I should see the API key creation confirmation")
def verify_api_key_creation(page: Page):
    """Verify API key creation confirmation."""
    # Verify the confirmation dialog is shown
    close_button = page.get_by_role("button", name="Close")
    expect(close_button).to_be_visible()
    print("API key creation confirmation displayed")

@when("I close the confirmation dialog")
def close_confirmation_dialog(page: Page):
    """Close the confirmation dialog."""
    page.get_by_role("button", name="Close").click()
    print("Closed confirmation dialog")

@then("I should see the API key in the list")
def verify_api_key_in_list(page: Page):
    """Verify the API key appears in the list."""
    global api_key_name
    api_key_cell = page.get_by_role("cell", name=api_key_name)
    expect(api_key_cell).to_be_visible()
    print(f"API key '{api_key_name}' is visible in the list")

@when("I select the API key")
def select_api_key(page: Page):
    """Select the API key."""
    global api_key_name
    page.get_by_role("cell", name=api_key_name).click()
    print(f"Selected API key '{api_key_name}'")

@when("I click Delete")
def click_delete(page: Page):
    """Click Delete button."""
    global api_key_name
    page.get_by_role("row", name=re.compile(f"{api_key_name}")).get_by_role("button").nth(1).click()
    print("Clicked Delete button")

@when("I confirm deletion")
def confirm_deletion(page: Page):
    """Confirm deletion."""
    page.get_by_role("button", name="Delete", exact=True).click()
    print("Confirmed deletion")

@then("the API key should be deleted successfully")
def verify_api_key_deleted(page: Page):
    """Verify the API key is deleted successfully."""
    global api_key_name
    # Wait for the deletion to complete
    page.wait_for_timeout(1000)  # Wait for 1 second for UI to update

    # Verify the API key is no longer in the list
    expect(page.get_by_role("cell", name=api_key_name)).to_have_count(0)
    print(f"API key '{api_key_name}' was successfully deleted")


