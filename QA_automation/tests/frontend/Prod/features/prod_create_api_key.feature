Feature: User should be able to create new API key and delete it
  As an user
  I want to create and delete API keys
  So that I can manage API access to the system

  @production
  Scenario: Create and delete an API key
    Given I navigate to the production login page
    When I enter valid credentials
    And I click the login button
    Then I should be logged in successfully
    And I should dismiss the NPS survey popup
    When I navigate to the Integration section
    And I click on New API Key
    And I fill in the API key details with a unique name
    And I set AWARE & SENSE permissions
    And I set Customer permissions
    And I click Generate key
    Then I should see the API key creation confirmation
    When I close the confirmation dialog
    Then I should see the API key in the list
    When I select the API key
    And I click Delete
    And I confirm deletion
    Then the API key should be deleted successfully
