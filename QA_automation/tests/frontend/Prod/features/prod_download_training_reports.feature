Feature: Download Training Reports
  As an user
  I want to download training reports from the admin dashboard
  So that I can analyze training reports CSVs, xlsx or zip files

  Background:
    Given I am on the production login page
    When I login with valid credentials
    Then I should be logged in successfully
    And I should dismiss any survey popups

  Scenario: Download all training report types with file format verification
    Given I am logged into the admin dashboard
    When I navigate to the Reporting section
    And I click on Training Reports
    And I dismiss any additional survey popups
    And I configure the training filters
    And I select training modules
    And I select all groups
    And I select all departments
    And I apply the training filters
    When I download all available training report types
    Then I should have downloaded at least 5 training report files
    And all downloaded files should be in valid data formats
    And all downloaded files should contain data
    And all files should be saved to a timestamped directory
    And the download directory should be cleaned up on success
