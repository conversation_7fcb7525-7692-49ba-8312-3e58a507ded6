Feature: User should be able to login and verify email templates are present
  As an user
  I want to log in to the production environment and verify email templates
  So that I can ensure the templates are available

  @production
  Scenario: Login to production and verify email templates
    Given I navigate to the production login page
    When I enter valid credentials
    And I click the login button
    Then I should be logged in successfully
    When I navigate to Template Management
    And I click on Email Templates
    Then I should see the SENSE Enrollment template
    And I should see the NINJIO Training Enrollment template

  @production
  Scenario: Navigate to Training Templates
    Given I navigate to the production login page
    When I enter valid credentials
    And I click the login button
    Then I should be logged in successfully
    When I navigate to Template Management
    And I click on Training Templates
    Then I should be on the templates page

  @production
  Scenario: Navigate to Phishing Templates
    Given I navigate to the production login page
    When I enter valid credentials
    And I click the login button
    Then I should be logged in successfully
    When I navigate to Template Management
    And I click on Phishing Templates
    Then I should be on the templates page
