Feature: User should be able to crete, edit and delete users; 
  As an user
  I want to be able to create, edit, and delete users
  So that I can manage user accounts in the system

  Background:
    Given I navigate to the production login page
    When I enter valid credentials
    And I click the login button
    Then I should be logged in successfully
    And I should dismiss the NPS survey popup
    When I navigate to User Management
    And I click on Users & Groups

  @production @user-creation
  Scenario: Create a new user
    When I click on New User
    And I fill in the user details with a unique email
    Then I should see language dropdowns on the form
    When I set language preferences
    And I click Apply
    Then I should see the user creation confirmation
    And I should store the created user's email for later tests

  @production @user-edit
  Scenario: Edit an existing user
    When I search for the previously created user
    And I select the user from the list
    And I click Edit
    And I enable the user
    And I click Apply
    Then I should see the edit confirmation
    When I click Edit again
    And I disable the user
    And I click Apply
    Then I should see the edit confirmation

  @production @user-deletion
  Scenario: Delete a user
    When I search for the previously created user
    And I select the user from the list
    And I click Delete
    Then I should see a confirmation dialog with the correct user details
    When I confirm deletion
    Then the user should be deleted successfully
