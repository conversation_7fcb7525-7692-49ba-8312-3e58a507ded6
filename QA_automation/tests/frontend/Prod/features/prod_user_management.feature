Feature: User Management in the Admin Panel

  As an admin
  I want to be able to create and edit users
  So I can manage platform users effectively.

  Scenario: Successfully create and edit a user
    Given I am logged in as "autotest2" with the password "@autotest123"
    When I navigate to the settings page and go to "Permissions"
    And I create a new user with the first name "<PERSON><PERSON>", last name "<PERSON>", email "<EMAIL>", and password "Test@123"
    Then the user "test_luana" should be successfully created
    When I edit the user "test_luana" swapping the first name from "<PERSON><PERSON>" to "Test" and last name from "Test" to "<PERSON><PERSON>"
    Then the user "test_luana" should be updated with names swapped
