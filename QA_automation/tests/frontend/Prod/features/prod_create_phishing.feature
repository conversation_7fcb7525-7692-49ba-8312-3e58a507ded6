Feature: Create Phishing Simulation
  As an user
  I want to create new phishing simulations
  So that I can test employee security awareness and response to phishing attacks

  Background:
    Given I am on the production login page
    When I login with valid credentials
    Then I should be logged in successfully
    And I should dismiss any survey popups

  Scenario: Create and start a new phishing simulation
    Given I am logged into the admin dashboard
    When I navigate to Campaign Management
    And I click on Phishing section
    And I click on New phishing simulation button
    And I fill in a unique phishing simulation name
    And I select the first phishing template with hover interaction
    And I select the second phishing template with hover interaction
    And I proceed to employee selection step
    And I select the employees tab
    And I search for and select a user
    And I proceed to the finish step
    And I finish creating the phishing simulation
    Then the phishing simulation should be created successfully
    And I should be able to select the created phishing simulation
    And I should be able to start the phishing simulation
    And the phishing simulation should start successfully
