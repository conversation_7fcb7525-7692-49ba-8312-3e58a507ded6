Feature: Edit, Clone, and Delete Training Simulation
  As an user
  I want to edit, clone, and delete training simulations with success
  So that I can manage training content and lifecycle

  Background:
    Given I am logged into the production environment
    And I navigate to the Training management page

  Scenario: Successfully edit, clone, and delete a training simulation
    Given I am on the training list page
    When I select an existing training simulation
    And I click the Edit button
    And I update the training name with a timestamp
    And I proceed through the training settings
    And I navigate to the Employees tab
    And I proceed to the final step
    And I save the changes
    Then the training should be successfully updated
    And I should be returned to the training list page
    And I should see the edited training name in the list
    When I select the previously edited training simulation
    And I click the Clone button
    And I update the clone name with a suffix
    And I proceed through the clone settings
    And I finish the clone process
    Then the training should be successfully cloned
    And I should see the cloned training in the list
    When I select the cloned training simulation for deletion
    And I click the Delete button using codegen selectors
    And I confirm the deletion using codegen selectors
    Then the cloned training should be successfully deleted
    And I should not see the cloned training in the list


