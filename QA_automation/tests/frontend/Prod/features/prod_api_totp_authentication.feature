Feature: Production API TOTP authentication
  As a user with two-factor authentication enabled
  I want to authenticate via API with TOTP token
  So that I can access the production system securely

  @production @api @totp
  Scenario: Successful API authentication with TOTP token
    Given I have valid production credentials with TOTP enabled
    When I make an API authentication request with TOTP token
    Then I should receive a successful response with JWT tokens

  @production @api @totp
  Scenario: Failed API authentication without TOTP token
    Given I have valid production credentials with TOTP enabled
    When I make an API authentication request without TOTP token
    Then I should receive an unauthorized response
    And I should see the TOTP required error message
