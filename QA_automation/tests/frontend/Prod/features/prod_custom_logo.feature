Feature: Custom Logo Management
  As an admin user
  I want to upload and manage custom logos in the admin panel
  So that I can customize the branding of the application

  Background:
    Given I am logged in as "autotest2" with the password "@autotest123"
    When I navigate to the settings page and go to "Branding"

  @production @custom-logo
  Scenario: Successfully upload and verify custom logo
    When I upload a custom logo file "ninjio_logo.png"
    Then the custom logo should be successfully uploaded
    And I should see the uploaded logo in the preview
    When I save the logo configuration
    Then the logo should be applied to the system
    And I should be able to verify the logo is displayed
