Feature: Smart Sequence Training Creation
  As a user of the production environment
  I want to create a smart training sequence
  So that I can deliver training modules in ascending  order

  Background:
    Given I am on the production login page
    When I login with valid credentials
    Then I should be logged in successfully
    And I should dismiss any survey popups

  @production @smart_training
  Scenario: Create and start a smart training sequence
    Given I am logged into the production dashboard
    When I navigate to Campaign Management
    And I navigate to Smart Training section
    And I click on New smart training button
    And I fill in a unique smart training name
    And I select multiple training modules in sequence
    And I proceed to email template selection
    And I select the NINJIO Training Enrollment email template
    And I proceed to sequence settings configuration
    And I configure sequence settings to "In Sequence"
    And I proceed to employee selection
    And I select an employee for the training
    And I proceed to the training summary step
    And I verify the training modules are in correct sequence order
    Then the smart training sequence should be created successfully
