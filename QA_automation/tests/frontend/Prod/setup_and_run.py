#!/usr/bin/env python3
"""
Setup and Run Script for Production Frontend Tests

This script helps set up the environment and run tests with correct Python paths
to avoid import errors.

Usage:
    python setup_and_run.py --setup    # Setup environment only
    python setup_and_run.py --test     # Run all tests
    python setup_and_run.py --headed   # Run tests with UI
    python setup_and_run.py --help     # Show help
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def find_repository_root():
    """Find the repository root directory (nemo-1)."""
    current_dir = Path(__file__).parent.absolute()
    
    # Look for repository root by checking for specific directories
    while current_dir.parent != current_dir:
        if (current_dir / "QA_automation").exists() and (current_dir / "front-react").exists():
            return current_dir
        current_dir = current_dir.parent
    
    # If not found, assume we're in the right place
    return Path.cwd()


def setup_environment():
    """Set up the Python environment with correct paths."""
    repo_root = find_repository_root()
    print(f"Repository root: {repo_root}")
    
    # Add repository root to Python path
    if str(repo_root) not in sys.path:
        sys.path.insert(0, str(repo_root))
    
    # Set PYTHONPATH environment variable
    current_pythonpath = os.environ.get('PYTHONPATH', '')
    if str(repo_root) not in current_pythonpath:
        if current_pythonpath:
            os.environ['PYTHONPATH'] = f"{repo_root}{os.pathsep}{current_pythonpath}"
        else:
            os.environ['PYTHONPATH'] = str(repo_root)
    
    print(f"PYTHONPATH set to: {os.environ['PYTHONPATH']}")
    return repo_root


def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = ['pytest', 'playwright', 'pytest-bdd']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("Please install dependencies first:")
        print("pip install -r QA_automation/tests/frontend/Prod/requirements.txt")
        return False
    
    print("✅ All required packages are installed")
    return True


def run_tests(repo_root, headed=False, specific_test=None):
    """Run the tests with correct environment setup."""
    os.chdir(repo_root)
    
    # Build pytest command
    cmd = ["python", "-m", "pytest", "QA_automation/tests/frontend/Prod", "-v"]
    
    if headed:
        cmd.append("--headed")
    
    if specific_test:
        cmd[-2] = f"QA_automation/tests/frontend/Prod/{specific_test}"
    
    print(f"Running command: {' '.join(cmd)}")
    print(f"Working directory: {os.getcwd()}")
    
    # Run the tests
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Setup and run Production Frontend Tests")
    parser.add_argument("--setup", action="store_true", help="Setup environment only")
    parser.add_argument("--test", action="store_true", help="Run all tests")
    parser.add_argument("--headed", action="store_true", help="Run tests with UI")
    parser.add_argument("--specific", type=str, help="Run specific test file")
    
    args = parser.parse_args()
    
    if not any([args.setup, args.test, args.headed, args.specific]):
        parser.print_help()
        return
    
    print("🚀 Setting up environment...")
    repo_root = setup_environment()
    
    if not check_dependencies():
        return
    
    if args.setup:
        print("✅ Environment setup complete!")
        print(f"Repository root: {repo_root}")
        print("You can now run tests manually with:")
        print("pytest QA_automation/tests/frontend/Prod -v")
        return
    
    if args.test or args.headed or args.specific:
        print("🧪 Running tests...")
        success = run_tests(
            repo_root, 
            headed=args.headed, 
            specific_test=args.specific
        )
        
        if success:
            print("✅ Tests completed successfully!")
        else:
            print("❌ Some tests failed or encountered errors")


if __name__ == "__main__":
    main()
