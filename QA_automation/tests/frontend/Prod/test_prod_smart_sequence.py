"""
Smart Sequence Training Creation Test - BDD Style
Tests the complete flow of creating and starting a smart training sequence in production environment
"""

import pytest
import re
from pytest_bdd import scenarios, given, when, then, parsers
from playwright.sync_api import Page, expect
import sys
import os
from datetime import datetime

# Add the utils directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))

from popup_handlers import handle_survey_popup, dismiss_popups
from login import login_to_production

# Import the feature file
scenarios('features/prod_smart_sequence.feature')

# Global variables to store test data
training_name = ""
timestamp = ""


# Background steps
@given("I am on the production login page")
def navigate_to_production_login_page(page: Page):
    """Navigate to the production login page."""
    pass  # Handled by login_to_production function


@when("I login with valid credentials")
def login_with_credentials(page: Page):
    """Login with production credentials and handle popups."""
    global timestamp, training_name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    training_name = f"autotest-smartsequence-{timestamp}"

    print("=== Starting Smart Sequence Training Test ===")
    print("Step 1: Logging into production environment...")
    login_to_production(page)
    print("✓ Login successful")


@then("I should be logged in successfully")
def verify_login_success(page: Page):
    """Verify successful login."""
    print("✓ Login to production successful!")


@then("I should dismiss any survey popups")
def dismiss_survey_popups(page: Page):
    """Survey popups are handled by login_to_production function."""
    print("Step 2: Handling popups and navigation...")
    handle_survey_popup(page)
    dismiss_popups(page)
    print("✓ Survey popups handled")


# Main scenario steps
@given("I am logged into the production dashboard")
def verify_dashboard_access(page: Page):
    """Verify access to the production dashboard."""
    print("✓ Dashboard access verified")


@when("I navigate to Campaign Management")
def navigate_to_campaign_management(page: Page):
    """Navigate to Campaign Management section."""
    page.goto("https://admin.goninjio.com/react/mass_delivery")
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(2000)


# Client selection not needed in production - test user has direct access


@when("I navigate to Smart Training section")
def navigate_to_smart_training(page: Page):
    """Navigate to the Smart Training section using exact working approach."""
    print("Step 3: Navigating to Smart Training...")

    # Use exact same navigation as working test
    page.get_by_role("button", name="Campaign Management").nth(1).click()
    page.get_by_role("region").get_by_text("PhishingTraining").click()
    page.get_by_role("button", name="Training").click()
    page.get_by_role("button", name=" Smart").click()
    print("✓ Navigated to Smart Training section")


@when("I click on New smart training button")
def click_new_smart_training(page: Page):
    """Click on the New smart training button."""
    print("Step 4: Creating smart training sequence...")

    # Use exact same approach as working test
    page.locator("#DcoyaUpgradedInput_undefined").get_by_role("textbox").click()
    page.locator("#DcoyaUpgradedInput_undefined").get_by_role("textbox").fill(training_name)
    print(f"✓ Training name set: {training_name}")


@when("I fill in a unique smart training name")
def fill_training_name(page: Page):
    """Fill in a unique training name with timestamp using exact working approach."""
    # This step is now handled in the previous step
    pass


@when("I select multiple training modules in sequence")
def select_training_modules(page: Page):
    """Select multiple training modules using exact working approach."""
    print("Step 5: Selecting training modules...")

    # Wait for the page to load completely (same as working test)
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(3000)  # Additional wait for dynamic content

    # Training modules are confirmed to exist in production

    # Use production training modules with full names as they appear in the DOM
    training_modules = [
        "AWARE ANIME S02|E03 - Side loading mobile apps",
        "AWARE ANIME S02|E02 - Wire Transfer Fraud",
        "AWARE ANIME S02|E05 - Ransomware",
        "AWARE ANIME S02|E04 - Default Passwords",
        "AWARE ANIME S02|E01 - Badge Surfing and Clean Desk Policy"
    ]

    # Use the exact same approach as the working staging test
    for module in training_modules:
        try:
            print(f"Looking for training module: {module}")

            # Use the card container ID directly (from object inspector: id="card_container_AWARE ANIME S02|E01 - Badge Surfing and Clean Desk Policy")
            module_card = page.locator(f"[id=\"card_container_{module}\"]")
            module_card.wait_for(state="visible", timeout=10000)

            # Wait for card to be visible
            module_card.wait_for(state="visible", timeout=10000)

            # Scroll the card into view
            module_card.scroll_into_view_if_needed()
            page.wait_for_timeout(1000)  # Wait after scrolling

            # HOVER over the card to make Select button visible (exact same as working test)
            print(f"Hovering over {module} card...")
            module_card.hover()
            page.wait_for_timeout(1000)  # Wait for hover effect

            # Wait for Select button to become visible after hover
            select_button = module_card.get_by_role("button", name="Select")
            select_button.wait_for(state="visible", timeout=5000)

            # Click the select button
            select_button.click()
            # Extract just the module ID for cleaner logging
            module_id = module.split(" ")[2]  # Extract "S02|E01" from "AWARE ANIME S02|E01 - ..."
            print(f"✓ Selected module: {module_id}")

            # Small wait between selections to prevent rapid scrolling (same as working test)
            page.wait_for_timeout(800)

        except Exception as e:
            print(f"❌ Failed to select {module}: {e}")
            # Continue with next module

    # Wait before clicking Next (same as working test)
    page.wait_for_timeout(2000)
    # Use specific ID to avoid ambiguity between multiple Next buttons (same as working test)
    page.locator("#campaign-next-create-button_cooldown-false").click()
    print("✓ Training modules selected, proceeding to next step")


@when("I proceed to email template selection")
def proceed_to_email_template(page: Page):
    """Proceed to email template selection step."""
    print("Step 6: Selecting email template...")


@when("I select the NINJIO Training Enrollment email template")
def select_email_template(page: Page):
    """Select the NINJIO Training Enrollment email template for production."""
    # Wait for email template page to load (same as working test)
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(2000)

    # Find the email template card for production
    try:
        # Try to find by card container ID first
        email_template_card = page.locator("[id*=\"NINJIO Training Enrollment\"]").first
        if not email_template_card.is_visible():
            # Fallback: find by text content
            template_text = page.get_by_text("NINJIO Training Enrollment")
            email_template_card = template_text.locator("xpath=ancestor::div[contains(@class, 'card') or contains(@id, 'card')]").first

        email_template_card.wait_for(state="visible", timeout=5000)

        # Scroll into view and hover to make Select button visible
        email_template_card.scroll_into_view_if_needed()
        page.wait_for_timeout(1000)

        print("Hovering over NINJIO Training Enrollment email template...")
        email_template_card.hover()
        page.wait_for_timeout(1500)  # Wait for hover effect

        # Wait for Select button to become visible and click it
        select_button = email_template_card.get_by_role("button", name="Select")
        select_button.wait_for(state="visible", timeout=3000)
        select_button.click()
        print("✓ Email template selected")

    except Exception as e:
        print(f"⚠ Error selecting email template: {e}")
        # Fallback: try to find any template with "NINJIO" and "Training" in the name
        try:
            fallback_template = page.locator("text*=NINJIO").and_(page.locator("text*=Training")).first
            fallback_card = fallback_template.locator("xpath=ancestor::div[contains(@class, 'card')]").first
            fallback_card.hover()
            page.wait_for_timeout(1000)
            fallback_select = fallback_card.get_by_role("button", name="Select")
            fallback_select.click()
            print("✓ Email template selected using fallback method")
        except Exception as e2:
            print(f"❌ Failed to select email template: {e2}")

    # Click Next button to proceed (exact same as working test)
    page.wait_for_timeout(1000)
    page.locator("#campaign-next-create-button_cooldown-false").click()
    print("✓ Proceeding to sequence settings")


@when("I proceed to sequence settings configuration")
def proceed_to_sequence_settings(page: Page):
    """Proceed to sequence settings configuration."""
    print("Step 7: Configuring sequence settings...")


@when('I configure sequence settings to "In Sequence"')
def configure_sequence_settings(page: Page):
    """Configure sequence settings to 'In Sequence' using exact working approach."""
    # Wait for the sequence settings page to load (same as working test)
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(5000)

    # Click on the dropdown to select "In Sequence" (same as working test)
    try:
        dropdown_button = page.locator("button#SelectSendingPlanSendTrainingDropdown_toggleDropdownButton_iconButton")
        dropdown_button.click()
        page.wait_for_timeout(1000)

        # Select "In Sequence" option
        in_sequence_option = page.locator("text='In Sequence'")
        in_sequence_option.click()
        print("✓ Selected 'In Sequence' from dropdown")

    except Exception as e:
        print(f"⚠ Error configuring sequence settings: {e}")

    # Wait and click Next (exact same as working test)
    page.wait_for_timeout(2000)
    page.locator("#campaign-next-create-button_cooldown-false").click()
    print("✓ Proceeding to employee selection")


@when("I proceed to employee selection")
def proceed_to_employee_selection(page: Page):
    """Proceed to employee selection step."""
    print("Step 8: Selecting employees...")


@when("I select an employee for the training")
def select_employee(page: Page):
    """Select an employee for the training using exact working approach."""
    # Wait for employee selection page to load (same as working test)
    page.wait_for_load_state("networkidle")
    page.wait_for_timeout(2000)

    # Click on Employees tab (same as working test)
    page.get_by_role("tab", name="Employees").click()
    page.wait_for_timeout(1000)

    # Search for specific roberto by email (same as working test)
    page.locator("#audience-search").get_by_role("textbox").click()
    page.get_by_role("textbox", name="Search").fill("<EMAIL>")
    page.wait_for_timeout(3000)  # Wait for search results to filter

    # Select the specific employee by email (same as working test)
    page.get_by_role("row").filter(has_text="<EMAIL>").get_by_label("Select row").check()
    print("✓ Employee '<EMAIL>' selected")

    # Click Next using specific ID (same as working test)
    page.wait_for_timeout(1000)
    page.locator("#campaign-next-create-button_cooldown-false").click()
    print("✓ Proceeding to final step")


@when("I proceed to the training summary step")
def proceed_to_summary(page: Page):
    """Proceed to the training summary step."""
    print("Verifying training modules order in Training Sequence...")


@when("I verify the training modules are in correct sequence order")
def verify_sequence_order(page: Page):
    """Verify that training modules are displayed in correct sequence order."""
    print("Verifying training modules order in Training Sequence...")

    # The system correctly sorts modules in ascending order regardless of selection order

    # Expected sequence should be in ascending order regardless of selection order
    # System should automatically sort: S02|E01, S02|E02, S02|E03, S02|E04, S02|E05
    expected_sequence = [
        ("Step 1:", "S02|E01"),
        ("Step 2:", "S02|E02"),
        ("Step 3:", "S02|E03"),
        ("Step 4:", "S02|E04"),
        ("Step 5:", "S02|E05")
    ]

    sequence_correct = True
    found_steps = []

    # Get all text from the page and verify the sequence order
    try:
        page_text = page.locator("body").inner_text()

        # Extract the sequence lines
        sequence_lines = []
        lines = page_text.split('\n')
        for line in lines:
            if line.strip().startswith('Step ') and 'S02|E' in line:
                sequence_lines.append(line.strip())

        print(f"Found sequence lines: {sequence_lines}")

        # Verify each expected step is in the correct position
        for i, (step_label, module_id) in enumerate(expected_sequence):
            if i < len(sequence_lines):
                actual_line = sequence_lines[i]
                if step_label in actual_line and module_id in actual_line:
                    print(f"✓ {step_label} {module_id} - Found in correct position")
                    found_steps.append(f"{step_label} {module_id}")
                else:
                    print(f"✗ {step_label} {module_id} - Expected but found: {actual_line}")
                    sequence_correct = False
            else:
                print(f"✗ {step_label} {module_id} - Missing from sequence")
                sequence_correct = False

    except Exception as e:
        print(f"✗ Error verifying sequence: {e}")
        sequence_correct = False

    # Assert that the sequence is correct
    assert sequence_correct, f"Training sequence is not in the expected order. Found: {found_steps}"
    print("✓ Training sequence order verification completed - All modules in correct order!")


@then("the smart training sequence should be created successfully")
def verify_training_creation_success():
    """Verify that the smart training sequence was created successfully."""
    print("✓ Smart training sequence order verification completed")
    print("=== Smart Sequence Training Test Completed Successfully ===")
    print("✓ Smart sequence training order validation completed successfully!")
