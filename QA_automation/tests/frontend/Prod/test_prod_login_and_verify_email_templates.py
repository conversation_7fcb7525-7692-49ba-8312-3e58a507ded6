import re
import pytest
from pytest_bdd import scenarios, given, when, then
from playwright.sync_api import Page, expect
from QA_automation.tests.frontend.utils.popup_handlers import handle_survey_popup

# Import the feature file
scenarios('features/prod_login.feature')

# Step definitions
@given("I navigate to the production login page")
def navigate_to_login_page(page: Page):
    """Navigate to the production login page."""
    page.goto('https://admin.goninjio.com/login')

@when("I enter valid credentials")
def enter_credentials(page: Page):
    """Enter valid login credentials."""
    page.get_by_role('textbox', name='Username').click()
    page.get_by_role('textbox', name='Username').fill('autotest')
    page.get_by_role('textbox', name='Password').click()
    page.get_by_role('textbox', name='Password').fill('@autotest123')

@when("I click the login button")
def click_login_button(page: Page):
    """Click the login button."""
    page.get_by_role('button', name='Login').click()

@then("I should be logged in successfully")
def verify_login_success(page: Page):
    """Verify successful login."""
    # Wait for navigation to complete after successful login
    page.goto('https://admin.goninjio.com/react/aware')
    expect(page).to_have_url(re.compile(r".*/react/.*"))

    # Handle the NPS survey popup that appears after login
    handle_survey_popup(page)

    print("Login to production successful!")

@when("I navigate to Template Management")
def navigate_to_template_management(page: Page):
    """Navigate to Template Management section."""
    page.get_by_role('button', name='Template Management').nth(1).click()

@when("I click on Email Templates")
def click_email_templates(page: Page):
    """Click on Email Templates button."""
    page.get_by_role('button', name='Email Templates').click()
    # Wait for the page to load
    page.wait_for_load_state("networkidle")

@then('I should see the SENSE Enrollment template')
def verify_sense_template_visible(page: Page):
    """Verify that SENSE Enrollment template is visible."""
    print("Checking for SENSE Enrollment template...")
    template = page.get_by_text("SENSE Enrollment")
    expect(template).to_be_visible(timeout=5000)
    print("✅ SENSE Enrollment template found")

@then('I should see the NINJIO Training Enrollment template')
def verify_ninjio_template_visible(page: Page):
    """Verify that NINJIO Training Enrollment template is visible."""
    print("Checking for NINJIO Training Enrollment template...")
    template = page.get_by_text("NINJIO Training Enrollment")
    expect(template).to_be_visible(timeout=5000)
    print("✅ NINJIO Training Enrollment template found")

@when("I click on Training Templates")
def click_training_templates(page: Page):
    """Click on Training Templates button."""
    page.get_by_role('button', name='Training Templates').click()

@when("I click on Phishing Templates")
def click_phishing_templates(page: Page):
    """Click on Phishing Templates button."""
    page.get_by_role('button', name='Phishing Templates').click()

@then("I should be on the templates page")
def verify_templates_page(page: Page):
    """Verify that we are on the templates page."""
    # The URL might be aware_content instead of containing 'templates'
    expect(page).to_have_url(re.compile(r".*(templates|aware_content).*"))
    print("Successfully navigated to templates page")
