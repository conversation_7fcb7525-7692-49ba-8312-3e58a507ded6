import pytest
from pytest_bdd import scenarios, given, when, then
from playwright.sync_api import Page, expect
import sys
import os
from datetime import datetime

# Add the utils directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))

from popup_handlers import handle_survey_popup, dismiss_popups
from login import login_to_production

# Import the feature file
scenarios('features/prod_download_phish_reports.feature')

# Global variables to store test data
downloads = []
download_dir = ""


# Background steps
@given("I am on the production login page")
def navigate_to_login_page(page: Page):
    """Navigate to the production login page."""
    # This will be handled by login_to_production function
    pass


@when("I login with valid credentials")
def login_with_credentials(page: Page):
    """Login with autotest credentials and handle popups."""
    login_to_production(page)


@then("I should be logged in successfully")
def verify_login_success(page: Page):
    """Verify successful login."""
    print("Login to production successful!")


@then("I should dismiss any survey popups")
def dismiss_survey_popups(page: Page):
    """Force dismiss any remaining survey popups."""
    dismiss_popups(page)
    print("Survey popups dismissed")


@given("I am logged into the admin dashboard")
def logged_into_dashboard(page: Page):
    """Verify we are logged into the admin dashboard."""
    # This step is satisfied by the background steps
    pass


@when("I navigate to the Reporting section")
def navigate_to_reporting(page: Page):
    """Navigate to the Reporting section."""
    page.get_by_role("button", name="Reporting").nth(1).click()
    print("Navigated to Reporting section")


@when("I click on Phishing Reports")
def click_phishing_reports(page: Page):
    """Click on Phishing Reports."""
    page.get_by_role("button", name="Phishing Reports").click()
    print("Clicked on Phishing Reports")


@when("I configure the report filters")
def configure_report_filters(page: Page):
    """Configure filters for phishing reports."""
    # Wait for the iframe to be fully loaded
    page.wait_for_timeout(3000)

    # Click on Campaign dropdown with timeout
    campaign_dropdown = page.locator("#root iframe").content_frame.get_by_role("button", name="Campaign creat...")
    try:
        campaign_dropdown.wait_for(state="visible", timeout=10000)
        campaign_dropdown.click()
        print("✓ Successfully opened Campaign dropdown")
    except Exception as e:
        print(f"Warning: Could not verify Campaign dropdown visibility: {e}")
        # Try to click anyway - the original test worked without this assertion
        campaign_dropdown.click()
        print("✓ Clicked Campaign dropdown (without visibility verification)")

    # Select "Unassigned simulations" with timeout
    unassigned_simulations = page.locator("#root iframe").content_frame.get_by_text("Unassigned simulations")
    try:
        unassigned_simulations.wait_for(state="visible", timeout=5000)
        unassigned_simulations.click()
        print("✓ Successfully clicked 'Unassigned simulations'")
    except Exception as e:
        print(f"Warning: Could not verify 'Unassigned simulations' visibility: {e}")
        unassigned_simulations.click()
        print("✓ Clicked 'Unassigned simulations' (without visibility verification)")

    # Click on the unassigned simulations button
    unassigned_button = page.locator("#root iframe").content_frame.get_by_role("button", name="Unassigned sim... ... (2)")
    try:
        unassigned_button.wait_for(state="visible", timeout=5000)
        unassigned_button.click()
        print("✓ Successfully selected 'Unassigned simulations' - dropdown shows selection")
    except Exception as e:
        print(f"Warning: Could not verify 'Unassigned simulations' button: {e}")
        unassigned_button.click()
        print("✓ Clicked 'Unassigned simulations' button (without visibility verification)")



@when("I select all available campaigns")
def select_all_campaigns(page: Page):
    """Select all available campaigns."""
    iframe = page.locator("#root iframe").content_frame

    # First, try to click "Select All" directly (works when no campaigns are selected)
    select_all_button = iframe.get_by_role("button", name="✓  Select All")

    try:
        if select_all_button.is_visible(timeout=3000):
            select_all_button.click()
            print("✓ Successfully clicked 'Select All' directly")
            page.wait_for_timeout(1000)
            return
    except Exception as e:
        print(f"'Select All' not immediately available: {e}")

    # If "Select All" is not available, we need to select some campaigns first
    # to make the selection buttons appear
    print("Attempting to select individual campaigns to activate selection buttons...")

    # Look for campaign checkboxes or selectable items
    campaign_selectors = [
        "input[type='checkbox']",
        "[role='checkbox']",
        "button:has-text('Select')",
        ".campaign-item",
        ".selectable",
        "tr td:first-child", # Table rows first column (often has checkboxes)
        "label"
    ]

    campaigns_selected = False

    for selector in campaign_selectors:
        if campaigns_selected:
            break

        elements = iframe.locator(selector)
        count = elements.count()

        if count > 0:
            print(f"Found {count} elements with selector: {selector}")
            # Try to click the first few elements to select some campaigns
            for i in range(min(3, count)):  # Select up to 3 campaigns
                try:
                    element = elements.nth(i)
                    if element.is_visible():
                        element.click()
                        print(f"Clicked element {i+1} with selector: {selector}")
                        page.wait_for_timeout(500)
                        campaigns_selected = True
                except Exception as e:
                    print(f"Could not click element {i+1}: {e}")

    if campaigns_selected:
        print("Selected some campaigns, now looking for 'Select None' and 'Select All' buttons")
        page.wait_for_timeout(1000)  # Wait for buttons to appear

        # Now try to click "Select None" to clear selections
        select_none_button = iframe.get_by_role("button", name="×  Select None")
        try:
            if select_none_button.is_visible(timeout=3000):
                select_none_button.click()
                print("✓ Successfully clicked 'Select None'")
                page.wait_for_timeout(500)
        except Exception as e:
            print(f"Could not click 'Select None': {e}")

        # Then click "Select All" to select all campaigns
        try:
            if select_all_button.is_visible(timeout=3000):
                select_all_button.click()
                print("✓ Successfully clicked 'Select All'")
                page.wait_for_timeout(1000)
            else:
                print("'Select All' button not visible after selecting campaigns")
        except Exception as e:
            print(f"Could not click 'Select All': {e}")
    else:
        print("Warning: Could not select any campaigns to activate selection buttons")
        print("Proceeding without campaign selection - may affect report results")


@when("I apply the filters")
def apply_filters(page: Page):
    """Apply the configured filters."""
    apply_button = page.locator("#root iframe").content_frame.get_by_text("Apply")

    try:
        # Verify Apply button is visible and clickable
        apply_button.wait_for(state="visible", timeout=5000)
        apply_button.click()
        print("✓ Successfully clicked Apply button")

        # Wait for the filters to be applied and page to update
        page.wait_for_timeout(2000)

        # Verify that the Apply action was successful by checking if we're still in the reports interface
        iframe_present = page.locator("#root iframe").is_visible(timeout=5000)
        if iframe_present:
            print("✓ Successfully applied filters - reports interface still available")
        else:
            print("Warning: Reports interface state changed after applying filters")

    except Exception as e:
        print(f"Warning: Could not verify Apply button: {e}")
        apply_button.click()
        print("✓ Clicked Apply button (without visibility verification)")
        page.wait_for_timeout(2000)


@when("I download all available report types")
def download_all_reports(page: Page):
    """Download all available report types."""
    global downloads
    downloads = []

    # Download 1: Dashboard stat report
    with page.expect_download() as download_info:
        page.locator("#root iframe").content_frame.locator("div:nth-child(3) > .fal").first.click()
    download = download_info.value
    downloads.append(download)
    print(f"Downloaded: {download.suggested_filename}")

    # Download 2: Dashboard stat report 2
    with page.expect_download() as download1_info:
        page.locator("#root iframe").content_frame.locator("div:nth-child(3) > .dashboard-stat2 > .display > div:nth-child(3) > .fal").click()
    download1 = download1_info.value
    downloads.append(download1)
    print(f"Downloaded: {download1.suggested_filename}")

    # Download 3: Dashboard stat report 3
    with page.expect_download() as download2_info:
        page.locator("#root iframe").content_frame.locator("div:nth-child(2) > .dashboard-stat2 > .display > div:nth-child(3) > .fal").click()
    download2 = download2_info.value
    downloads.append(download2)
    print(f"Downloaded: {download2.suggested_filename}")

    # Download 4: Report icons container
    with page.expect_download() as download3_info:
        page.locator("#root iframe").content_frame.locator(".reprot-icons-container > div:nth-child(2) > .fal").click()
    download3 = download3_info.value
    downloads.append(download3)
    print(f"Downloaded: {download3.suggested_filename}")

    # Download 5: Results CSV (explicitly labeled)
    with page.expect_download() as download4_info:
        page.locator("#root iframe").content_frame.get_by_text("Results CSV").first.click()
    download4 = download4_info.value
    downloads.append(download4)
    print(f"Downloaded: {download4.suggested_filename}")

    # Download 6: Score CSV (explicitly labeled)
    with page.expect_download() as download5_info:
        page.locator("#root iframe").content_frame.get_by_text("Score CSV").first.click()
    download5 = download5_info.value
    downloads.append(download5)
    print(f"Downloaded: {download5.suggested_filename}")


@then("I should have downloaded 6 report files")
def verify_download_count():
    """Verify that 6 files were downloaded."""
    assert len(downloads) == 6, f"Expected 6 downloads, got {len(downloads)}"
    print(f"Successfully downloaded {len(downloads)} files")


@then("all downloaded files should be CSV format")
def verify_csv_format():
    """Verify all downloaded files are CSV format."""
    for download in downloads:
        assert download.suggested_filename.endswith('.csv'), f"Expected CSV file, got: {download.suggested_filename}"

    csv_files = [f.suggested_filename for f in downloads if f.suggested_filename.endswith('.csv')]
    assert len(csv_files) == 6, f"Expected all 6 files to be CSV, but only {len(csv_files)} are CSV files"
    print("All downloaded files are in CSV format")


@then("all downloaded files should have unique names")
def verify_unique_names():
    """Verify all downloaded files have unique names."""
    filenames = [download.suggested_filename for download in downloads]
    assert len(set(filenames)) == len(filenames), "Some downloaded files have duplicate names"
    print("All downloaded files have unique names")


@then("all downloaded files should contain data")
def verify_files_contain_data():
    """Verify all downloaded files contain data."""
    global download_dir
    download_dir = os.path.join(os.path.dirname(__file__), 'downloads', f"phish_reports_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    os.makedirs(download_dir, exist_ok=True)

    for i, download in enumerate(downloads, 1):
        file_path = os.path.join(download_dir, download.suggested_filename)
        download.save_as(file_path)

        # Verify the file was saved and has content
        assert os.path.exists(file_path), f"Downloaded file {download.suggested_filename} was not saved"
        file_size = os.path.getsize(file_path)
        assert file_size > 0, f"Downloaded file {download.suggested_filename} is empty (0 bytes)"
        print(f"Verified download {i}: {download.suggested_filename} ({file_size} bytes)")

    print("All downloaded files contain data")


@then("all files should be saved to a timestamped directory")
def verify_timestamped_directory():
    """Verify all files are saved to a timestamped directory."""
    assert download_dir != "", "Download directory was not created"
    assert os.path.exists(download_dir), f"Download directory {download_dir} does not exist"

    # Verify all files are in the directory
    saved_files = os.listdir(download_dir)
    assert len(saved_files) == 6, f"Expected 6 files in directory, found {len(saved_files)}"

    print(f"All files saved to timestamped directory: {download_dir}")
    print("Download phishing reports test completed successfully")

    # Clean up download directory on successful test completion
    import shutil
    try:
        shutil.rmtree(download_dir)
        print(f"✓ Cleaned up download directory: {download_dir}")
    except Exception as cleanup_error:
        print(f"Warning: Could not clean up download directory: {cleanup_error}")
