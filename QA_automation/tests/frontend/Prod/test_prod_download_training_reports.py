import pytest
import re
from pytest_bdd import scenarios, given, when, then
from playwright.sync_api import Page, expect
import sys
import os
from datetime import datetime

# Add the utils directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))

from popup_handlers import handle_survey_popup

# Import the feature file
scenarios('features/prod_download_training_reports.feature')

# Global variables to store test data
downloads = []
download_dir = ""


# Background steps
@given("I am on the production login page")
def navigate_to_login_page(page: Page):
    """Navigate to the production login page."""
    page.goto("https://admin.goninjio.com/login")


@when("I login with valid credentials")
def login_with_credentials(page: Page):
    """Login with autotest credentials."""
    page.get_by_role("textbox", name="Username").click()
    page.get_by_role("textbox", name="Username").fill("autotest")
    page.get_by_role("textbox", name="Password").click()
    page.get_by_role("textbox", name="Password").fill("@autotest123")
    page.get_by_role("button", name="Login").click()


@then("I should be logged in successfully")
def verify_login_success(page: Page):
    """Verify successful login."""
    page.goto("https://admin.goninjio.com/react/aware")
    print("Login to production successful!")


@then("I should dismiss any survey popups")
def dismiss_survey_popups(page: Page):
    """Handle survey popup after login using improved popup handler."""
    # Use the improved popup handler with environment-aware timeouts
    handle_survey_popup(page)
    print("Survey popups dismissed")


@given("I am logged into the admin dashboard")
def logged_into_dashboard(page: Page):
    """Verify we are logged into the admin dashboard."""
    # This step is satisfied by the background steps
    pass


@when("I navigate to the Reporting section")
def navigate_to_reporting(page: Page):
    """Navigate to the Reporting section."""
    page.get_by_role("button", name="Reporting").nth(1).click()
    print("Navigated to Reporting section")


@when("I click on Training Reports")
def click_training_reports(page: Page):
    """Click on Training Reports."""
    page.get_by_role("button", name="Training Reports").click()
    print("Clicked on Training Reports")


@when("I dismiss any additional survey popups")
def dismiss_additional_survey_popups(page: Page):
    """Handle additional survey popups that may appear after navigation."""
    # Use the improved popup handler with environment-aware timeouts
    handle_survey_popup(page)
    print("Additional survey popups handled")


@when("I configure the training filters")
def configure_training_filters(page: Page):
    """Configure training filters dropdown."""
    page.locator("#trainingsDropdown_toggleDropdownButton_iconButton").click()
    page.locator(".MuiBackdrop-root").click()
    page.locator("#trainingsDropdown_toggleDropdownButton_iconButton").click()
    print("Configured training filters dropdown")


@when("I select training modules")
def select_training_modules(page: Page):
    """Select specific training modules with verification."""
    try:
        # Verify and select "710 test ml8" module
        test_module = page.get_by_text("710 test ml8")
        test_module.wait_for(state="visible", timeout=5000)
        test_module.click()
        print("✓ Successfully selected '710 test ml8' module")
    except Exception as e:
        print(f"Warning: Could not verify '710 test ml8' module: {e}")
        page.get_by_text("710 test ml8").click()
        print("✓ Clicked '710 test ml8 module (without visibility verification)")

    try:
        # Verify and select "AWARE ANIME S02|E01 - Badge" module
        anime_module = page.get_by_text("AWARE ANIME S02|E01 - Badge")
        anime_module.wait_for(state="visible", timeout=5000)
        anime_module.click()
        print("✓ Successfully selected 'AWARE ANIME S02|E01 - Badge' module")
    except Exception as e:
        print(f"Warning: Could not verify 'AWARE ANIME S02|E01 - Badge' module: {e}")
        page.get_by_text("AWARE ANIME S02|E01 - Badge").click()
        print("✓ Clicked 'AWARE ANIME S02|E01 - Badge' module (without visibility verification)")

    # Close dropdown
    dropdown_button = page.locator("#trainingsDropdown_toggleDropdownButton_iconButton")
    try:
        dropdown_button.wait_for(state="visible", timeout=5000)
        dropdown_button.click()
        print("✓ Successfully closed training modules dropdown")
    except Exception as e:
        print(f"Warning: Could not verify dropdown close button: {e}")
        dropdown_button.click()
        print("✓ Clicked dropdown close button (without visibility verification)")

    print("Selected training modules")


@when("I select all groups")
def select_all_groups(page: Page):
    """Select all groups filter."""
    page.locator("#groupsDropdown_toggleDropdownButton_iconButton").click()
    page.get_by_text("Select all").click()
    page.locator("#groupsDropdown_toggleDropdownButton_iconButton").click()
    print("Selected all groups")


@when("I select all departments")
def select_all_departments(page: Page):
    """Select all departments filter."""
    page.locator("#departmentDropdown_toggleDropdownButton_iconButton").click()
    page.get_by_text("Select all").click()
    page.locator("#departmentDropdown_toggleDropdownButton_iconButton").click()
    print("Selected all departments")


@when("I apply the training filters")
def apply_training_filters(page: Page):
    """Apply the configured training filters."""
    page.get_by_role("button", name=" Apply").click()
    print("Applied training filters")


@when("I download all available training report types")
def download_all_training_reports(page: Page):
    """Download all available training report types."""
    global downloads
    downloads = []

    # Download 1: First training report
    with page.expect_download() as download_info:
        page.locator(".sc-bbSZdi > div:nth-child(2) > div > .MuiButtonBase-root").first.click()
    download = download_info.value
    downloads.append(download)
    print(f"Downloaded: {download.suggested_filename}")

    # Download 2: Executive report (PDF format first)
    page.locator("div:nth-child(4) > div:nth-child(2) > div > .MuiButtonBase-root").click()
    with page.expect_download() as download1_info:
        page.get_by_role("button", name="Download", exact=True).click()
    download1 = download1_info.value
    downloads.append(download1)
    print(f"Downloaded: {download1.suggested_filename}")

    # Download 3: Executive report (CSV format)
    page.locator("#executive-fileformat-report_toggleDropdownButton_iconButton").click()
    page.get_by_title("Csv").locator("li").click()
    with page.expect_download() as download2_info:
        page.get_by_role("button", name="Download", exact=True).click()
    download2 = download2_info.value
    downloads.append(download2)
    print(f"Downloaded: {download2.suggested_filename}")

    # Download 4: Overall User Engagement
    with page.expect_download() as download3_info:
        page.locator("div").filter(has_text=re.compile(r"^Overall User EngagementDownload$")).locator("#system-button_cooldown-false").click()
    download3 = download3_info.value
    downloads.append(download3)
    print(f"Downloaded: {download3.suggested_filename}")

    # Download 5: Leaderboard
    with page.expect_download() as download4_info:
        page.locator("div").filter(has_text=re.compile(r"^LeaderboardHow do we calculate\?DownloadSearch$")).locator("#system-button_cooldown-false").click()
    download4 = download4_info.value
    downloads.append(download4)
    print(f"Downloaded: {download4.suggested_filename}")

    # Download 6: Additional report (optional)
    try:
        with page.expect_download(timeout=15000) as download5_info:
            download6_button = page.get_by_role("button", name=" Download").nth(2)
            download6_button.click()
            # Wait a moment and click again to ensure it's clicked
            page.wait_for_timeout(1000)
            try:
                download6_button.click()
                print("Download 6 button clicked twice to ensure activation")
            except:
                print("Second click on Download 6 button failed, but first click may have worked")
        download5 = download5_info.value
        downloads.append(download5)
        print(f"Downloaded: {download5.suggested_filename}")
    except Exception as e:
        print(f"Download 6 not available or failed: {e}")

    # Download 7: Final report (optional)
    try:
        with page.expect_download(timeout=15000) as download6_info:
            page.get_by_role("button", name=" Download").nth(3).click()
        download6 = download6_info.value
        downloads.append(download6)
        print(f"Downloaded: {download6.suggested_filename}")
    except Exception as e:
        print(f"Download 7 not available or failed: {e}")


@then("I should have downloaded at least 5 training report files")
def verify_download_count():
    """Verify that at least 5 files were downloaded."""
    assert len(downloads) >= 5, f"Expected at least 5 downloads, got {len(downloads)}"
    print(f"Successfully downloaded {len(downloads)} files")


@then("all downloaded files should be in valid data formats")
def verify_file_formats():
    """Verify all downloaded files are in valid data formats."""
    for download in downloads:
        assert download.suggested_filename.endswith(('.csv', '.xlsx', '.zip')), f"Expected CSV, Excel, or ZIP file, got: {download.suggested_filename}"

    # Count data files by type
    filenames = [download.suggested_filename for download in downloads]
    csv_count = len([f for f in filenames if f.endswith('.csv')])
    xlsx_count = len([f for f in filenames if f.endswith('.xlsx')])
    zip_count = len([f for f in filenames if f.endswith('.zip')])
    other_count = len(filenames) - csv_count - xlsx_count - zip_count

    print(f"File type breakdown: {csv_count} CSV, {xlsx_count} Excel, {zip_count} ZIP, {other_count} other formats")
    print("All downloaded files are in valid data formats")


@then("all downloaded files should contain data")
def verify_files_contain_data():
    """Verify all downloaded files contain data."""
    global download_dir
    download_dir = os.path.join(os.path.dirname(__file__), 'downloads', f"training_reports_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    os.makedirs(download_dir, exist_ok=True)

    for i, download in enumerate(downloads, 1):
        file_path = os.path.join(download_dir, download.suggested_filename)
        download.save_as(file_path)

        # Verify the file was saved and has content
        assert os.path.exists(file_path), f"Downloaded file {download.suggested_filename} was not saved"
        file_size = os.path.getsize(file_path)
        assert file_size > 0, f"Downloaded file {download.suggested_filename} is empty (0 bytes)"
        print(f"Verified download {i}: {download.suggested_filename} ({file_size} bytes)")

    print("All downloaded files contain data")


@then("all files should be saved to a timestamped directory")
def verify_timestamped_directory():
    """Verify all files are saved to a timestamped directory."""
    assert download_dir != "", "Download directory was not created"
    assert os.path.exists(download_dir), f"Download directory {download_dir} does not exist"

    # Verify all files are in the directory
    saved_files = os.listdir(download_dir)
    # Note: Some files may have duplicate names and overwrite each other
    assert len(saved_files) >= 3, f"Expected at least 3 unique files in directory, found {len(saved_files)}"
    print(f"Found {len(saved_files)} unique files in directory (some downloads may have duplicate names)")

    print(f"All files saved to timestamped directory: {download_dir}")
    print("Download training reports test completed successfully")


@then("the download directory should be cleaned up on success")
def cleanup_download_directory():
    """Clean up download directory on successful test completion."""
    import shutil
    try:
        shutil.rmtree(download_dir)
        print(f"✓ Cleaned up download directory: {download_dir}")
    except Exception as cleanup_error:
        print(f"Warning: Could not clean up download directory: {cleanup_error}")
