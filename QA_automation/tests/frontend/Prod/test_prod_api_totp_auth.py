"""
API TOTP Authentication Tests for Production Environment

This module contains BDD-style API tests for verifying TOTP authentication
via POST requests to the production token endpoint.
"""

import pytest
import requests
import pyotp
import os
from pytest_bdd import scenarios, given, when, then

# Import the feature file
scenarios('features/prod_api_totp_authentication.feature')

# Production configuration from environment variables
API_URL = os.getenv("PROD_API_URL", "https://admin.goninjio.com/api/token/")
USERNAME = os.getenv("PROD_API_USERNAME")
PASSWORD = os.getenv("PROD_API_PASSWORD")
TOTP_SECRET = os.getenv("PROD_API_TOTP_SECRET")

# Validate required environment variables
def validate_environment_variables():
    """Validate that all required environment variables are set."""
    missing_vars = []

    if not USERNAME:
        missing_vars.append("PROD_API_USERNAME")
    if not PASSWORD:
        missing_vars.append("PROD_API_PASSWORD")
    if not TOTP_SECRET:
        missing_vars.append("PROD_API_TOTP_SECRET")

    if missing_vars:
        pytest.skip(f"Missing required environment variables: {', '.join(missing_vars)}")

# Global variable to store the API response
api_response = None


def generate_totp_token() -> str:
    """Generate current TOTP token with timing tolerance."""
    validate_environment_variables()

    totp = pyotp.TOTP(TOTP_SECRET)
    # Try current token first
    current_token = totp.now()
    return current_token


def make_auth_request(include_totp: bool = True) -> requests.Response:
    """
    Make authentication request to the API.

    Args:
        include_totp: Whether to include TOTP token in request

    Returns:
        Response object
    """
    payload = {
        "username": USERNAME,
        "password": PASSWORD
    }

    if include_totp:
        import time
        current_time = time.time()
        totp_token = generate_totp_token()
        payload["totp_token"] = totp_token
        print(f"🔐 Using TOTP token: {totp_token} at timestamp: {current_time}")
        print(f"⏰ Token changes every 30s, next change in ~{30 - (current_time % 30):.1f}s")

    headers = {
        "Content-Type": "application/json"
    }

    response = requests.post(
        API_URL,
        json=payload,
        headers=headers,
        timeout=30
    )

    return response


@given("I have valid production credentials with TOTP enabled")
def setup_credentials():
    """Setup production credentials with TOTP."""
    validate_environment_variables()
    print("✓ Production credentials loaded from environment variables")


@when("I make an API authentication request with TOTP token")
def make_request_with_totp():
    """Make API request with TOTP token."""
    global api_response
    api_response = make_auth_request(include_totp=True)


@when("I make an API authentication request without TOTP token")
def make_request_without_totp():
    """Make API request without TOTP token."""
    global api_response
    api_response = make_auth_request(include_totp=False)


@then("I should receive a successful response with JWT tokens")
def verify_successful_response():
    """Verify successful response with JWT tokens."""
    assert api_response.status_code == 200, (
        f"Expected 200 OK, got {api_response.status_code}. "
        f"Response: {api_response.text}"
    )


@then("I should receive an unauthorized response")
def verify_unauthorized_response():
    """Verify unauthorized response."""
    assert api_response.status_code == 401, (
        f"Expected 401 Unauthorized, got {api_response.status_code}. "
        f"Response: {api_response.text}"
    )


@then("I should see the TOTP required error message")
def verify_totp_error_message():
    """Verify TOTP required error message."""
    response_data = api_response.json()
    expected_message = "TOTP token is required for users with 2FA enabled"

    # Check if the error message is present in the response
    error_found = False
    if isinstance(response_data, dict):
        # Check various possible error fields
        error_fields = ['error', 'message', 'detail', 'non_field_errors']
        for field in error_fields:
            if field in response_data:
                error_value = response_data[field]
                if isinstance(error_value, list):
                    error_found = any(expected_message in str(msg) for msg in error_value)
                else:
                    error_found = expected_message in str(error_value)
                if error_found:
                    break

    assert error_found, (
        f"Expected error message '{expected_message}' not found in response. "
        f"Response: {response_data}"
    )



