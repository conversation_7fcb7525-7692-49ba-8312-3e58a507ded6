# UUID: 4d72ecdf-2378-43cb-b015-9d7b66dc61f8

import pytest
from pytest_bdd import scenarios, given, when, then
from playwright.sync_api import Page
import sys
import os


# Add the QA_automation directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))

from pages.prod.prod_branding_page import ProdBrandingPage
from pages.prod.prod_login_page import ProdLoginPage

# Load scenarios from the feature file
scenarios('features/prod_custom_logo.feature')

# Global variables to store test state
branding_page_instance = None
original_logo_info = None
report_button_path = None


@given('I am logged in as "autotest2" with the password "@autotest123"')
def login_to_admin(page: Page):
    """Log into the admin interface using ProdLoginPage."""
    try:
        # Navigate to the login page
        page.goto("https://admin.goninjio.com/")
        page.wait_for_load_state("networkidle")

        # Use the ProdLoginPage page object
        login_page = ProdLoginPage(page)
        login_page.login("autotest2", "@autotest123")

        print("✅ Login completed using ProdLoginPage")

    except Exception as e:
        print(f"❌ Login failed: {e}")
        raise AssertionError(f"Failed to login: {e}")


@when('I navigate to the settings page and go to "Branding"')
def navigate_to_custom_logo_settings(page: Page):
    """Navigate to Phish Reporting > Reporting settings for custom logo."""
    global branding_page_instance, original_logo_info

    try:
        # Navigate to main page first
        page.goto('https://admin.goninjio.com/react/aware')
        page.wait_for_timeout(2000)


        # Click Phish Reporting button 
        phish_button = page.get_by_role("button", name="Phish Reporting").nth(1)
        phish_button.click()
        print("✓ Clicked Phish Reporting using codegen selector")

        page.wait_for_timeout(1000)

        # Click Reporting settings button
        settings_button = page.get_by_role("button", name="Reporting settings").nth(1)
        settings_button.click()
        print("✓ Clicked Reporting settings using codegen selector")

        page.wait_for_timeout(2000)

        # Initialize branding page and capture original logo state
        branding_page_instance = ProdBrandingPage(page)
        original_logo_info = branding_page_instance.capture_original_logo_state()
        print(f"📸 Original logo captured: {original_logo_info}")

        print("✓ Successfully navigated to custom logo settings")

    except Exception as e:
        print(f"❌ Error navigating to branding: {e}")
        raise AssertionError(f"Failed to navigate to branding: {e}")


@when('I upload a custom logo file "ninjio_logo.png"')
def upload_custom_logo(page: Page):
    """Upload a custom logo file."""
    global report_button_path

    print("🔄 STARTING UPLOAD STEP - upload_custom_logo function called")

    try:
        # Use NINJIO logo from resources folder
        report_button_path = "ninjio_logo.png"
        resources_dir = os.path.join(os.path.dirname(__file__), 'resources')
        full_logo_path = os.path.join(resources_dir, report_button_path)

        # Verify file exists
        if not os.path.exists(full_logo_path):
            raise FileNotFoundError(f"❌ Logo file not found at {full_logo_path}")

        print(f"📂 Logo path: {full_logo_path}")

        # Simplified upload approach - target specific image file input
        print(f"📁 Attempting to upload file: {full_logo_path}")

        # Use specific selector for image file input (PNG)
        image_file_input = page.locator('input[type="file"][accept="image/png"]')
        image_file_input.set_input_files(full_logo_path)
        page.wait_for_timeout(2000)
        print("✅ Logo file uploaded successfully")

    except Exception as e:
        print(f"❌ Error uploading logo: {e}")
        raise AssertionError(f"Failed to upload logo: {e}")


@then('the custom logo should be successfully uploaded')
def verify_logo_uploaded(page: Page):
    """Verify that the logo was uploaded successfully."""
    print("✅ Logo upload step completed")


@then('I should see the uploaded logo in the preview')
def verify_logo_preview(page: Page):
    """Verify that the logo preview is updated."""
    global branding_page_instance, original_logo_info

    try:
        if not branding_page_instance:
            branding_page_instance = ProdBrandingPage(page)

        # Use the branding page instance to verify logo changed (this method works!)
        if branding_page_instance.verify_logo_changed(original_logo_info):
            print("✅ Logo preview verified - logo has changed from original")
        else:
            print("❌ LOGO PREVIEW FAILED!")
            raise AssertionError("TEST FAILED - Logo did not change in preview")

        print("✅ Logo preview step completed")

    except Exception as e:
        print(f"❌ Error verifying logo preview: {e}")
        raise AssertionError(f"Failed to verify logo preview: {e}")


@when('I save the logo configuration')
def save_logo_configuration(page: Page):
    """Save the logo configuration."""
    try:
        # Use codegen Save button selector with force to bypass popup
        save_button = page.get_by_role("button", name=" Save")
        save_button.click(force=True)
        print("✓ Clicked Save button using codegen selector (forced)")
        page.wait_for_timeout(2000)

    except Exception as e:
        print(f"❌ CRITICAL ERROR - Save failed: {e}")
        raise AssertionError(f"TEST FAILED - Error saving: {e}")


@then('the logo should be applied to the system')
def verify_logo_applied(page: Page):
    """Verify that the logo is applied to the system."""
    global branding_page_instance, original_logo_info

    try:
        if not branding_page_instance:
            branding_page_instance = ProdBrandingPage(page)

        # Use the branding page instance to verify logo changed (this method works!)
        if branding_page_instance.verify_logo_changed(original_logo_info):
            print("✅ Final verification - logo successfully applied and changed")
        else:
            print("❌ FINAL VERIFICATION FAILED!")
            raise AssertionError("TEST FAILED - Logo was not applied to system")

        print(f"✅ Custom logo test completed for {report_button_path}")

    except Exception as e:
        print(f"❌ Error in final verification: {e}")
        raise AssertionError(f"Failed final verification: {e}")


@then('I should be able to verify the logo is displayed')
def verify_logo_displayed(page: Page):
    """Final verification that logo is displayed."""
    print("✅ Logo display verification completed")
