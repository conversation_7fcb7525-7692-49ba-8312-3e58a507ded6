"""
Utility module for handling popups and other common UI elements in tests.
"""
from playwright.sync_api import Page


# Environment-aware timeout configuration
TIMEOUTS = {
    'headless': {
        'popup_detection': 5000,      # 5 seconds to detect popup in headless
        'button_wait': 4000,          # 4 seconds to wait for buttons in headless
        'action_wait': 2000,          # 2 seconds between actions in headless
        'popup_disappear': 3000       # 3 seconds to wait for popup to disappear
    },
    'headed': {
        'popup_detection': 3000,      # 3 seconds to detect popup in headed mode
        'button_wait': 2000,          # 2 seconds to wait for buttons in headed mode
        'action_wait': 1000,          # 1 second between actions in headed mode
        'popup_disappear': 1000       # 1 second to wait for popup to disappear
    }
}


def is_headless_mode(page: Page) -> bool:
    """
    Detect if the browser is running in headless mode.
    Returns True if headless, False if headed.
    """
    try:
        # Check if window dimensions are available (headed mode has real window)
        has_window = page.evaluate("() => window.outerWidth > 0 && window.outerHeight > 0")
        return not has_window
    except:
        # If we can't determine, assume headless for safer (longer) timeouts
        return True


def get_timeouts(page: Page) -> dict:
    """
    Get appropriate timeouts based on browser mode (headless vs headed).
    """
    mode = 'headless' if is_headless_mode(page) else 'headed'
    timeouts = TIMEOUTS[mode]
    print(f"Using {mode} mode timeouts: {timeouts}")
    return timeouts


def handle_survey_popup(page: Page) -> None:
    """
    Helper function to handle the NPS survey popup that appears at the bottom right of the page.
    Tries multiple approaches to dismiss the popup:
    1. Click the Skip button
    2. Click the arrow/minimize button at the top of the popup
    3. Click the Next button

    Uses environment-aware timeouts for better reliability in headless vs headed mode.
    """
    try:
        # Get appropriate timeouts for current browser mode
        timeouts = get_timeouts(page)

        # Wait for the survey popup to appear (longer wait for headless mode)
        page.wait_for_timeout(timeouts['action_wait'])

        # Look for the NPS survey popup
        # First check if the survey is visible by looking for its title text
        survey_visible = page.get_by_text("How likely are you to recommend us to a friend or colleague?").is_visible(timeout=timeouts['popup_detection'])

        if survey_visible:
            print("NPS survey popup detected")

            # APPROACH 1: Try to click the Skip button in the survey multiple times
            skip_button = page.get_by_role("button", name="Skip")
            if skip_button.is_visible(timeout=timeouts['button_wait']):
                print("Clicking Skip button on NPS survey")
                # Click Skip multiple times to ensure it's fully dismissed
                for i in range(3):  # Try up to 3 times
                    try:
                        skip_button.click()
                        print(f"Clicked Skip button (attempt {i+1})")
                        page.wait_for_timeout(timeouts['action_wait'])  # Wait for the popup to disappear

                        # Check if Skip button is still visible
                        if not skip_button.is_visible(timeout=timeouts['popup_disappear']):
                            print("Skip button no longer visible, popup dismissed")
                            break
                    except Exception as e:
                        print(f"Error clicking Skip button on attempt {i+1}: {e}")
                        # Continue to next attempt

                # After clicking Skip, look for and click the Close button
                close_button = page.get_by_role("button", name="Close")
                if close_button.is_visible(timeout=timeouts['button_wait']):
                    print("Found Close button after Skip, clicking it")
                    close_button.click()
                    page.wait_for_timeout(timeouts['action_wait'])

                # Also try alternative Close button selectors
                close_selectors = [
                    "button:has-text('Close')",
                    ".close-button",
                    ".btn-close",
                    "[aria-label='close']"
                ]

                for selector in close_selectors:
                    close_btn = page.locator(selector)
                    if close_btn.is_visible(timeout=timeouts['popup_disappear']):
                        print(f"Found Close button with selector: {selector}")
                        close_btn.click()
                        page.wait_for_timeout(timeouts['action_wait'])
                        break
            else:
                print("Skip button not found on NPS survey")

                # Try alternative selector for Skip button
                skip_button_alt = page.locator("button:has-text('Skip')")
                if skip_button_alt.is_visible(timeout=timeouts['button_wait']):
                    print("Clicking Skip button (alternative selector)")
                    skip_button_alt.click()
                    page.wait_for_timeout(timeouts['action_wait'])
                else:
                    # APPROACH 2: Try to click the arrow/minimize button at the top of the popup
                    # The arrow is likely in the top-right corner of the popup
                    print("Trying to click the minimize arrow on the survey popup")

                    # Try to find the arrow/minimize button by looking for elements in the popup header
                    # First try to locate the popup container
                    popup_container = page.locator("div").filter(has_text="How likely are you to recommend us to a").first

                    if popup_container.is_visible(timeout=timeouts['button_wait']):
                        # Get the bounding box of the popup container
                        box = popup_container.bounding_box()
                        if box:
                            # Click in the top-right corner of the popup (where the arrow/minimize button likely is)
                            # Adjust x and y coordinates to target the top-right corner
                            page.mouse.click(box["x"] + box["width"] - 10, box["y"] + 10)
                            print("Clicked on the top-right corner of the popup")
                            page.wait_for_timeout(timeouts['action_wait'])

                    # Try to find the arrow specifically - it might be a caret or chevron icon
                    arrow_locators = [
                        "svg[data-icon='caret-up'], svg[data-icon='caret-down'], svg[data-icon='chevron-up'], svg[data-icon='chevron-down']",
                        ".caret, .chevron, .arrow, .minimize-icon",
                        "button.minimize, button.collapse, button.toggle"
                    ]

                    for locator in arrow_locators:
                        arrow = page.locator(locator)
                        if arrow.is_visible(timeout=timeouts['popup_disappear']):
                            print(f"Found arrow/minimize button with selector: {locator}")
                            arrow.click()
                            page.wait_for_timeout(timeouts['action_wait'])
                            break

                    # APPROACH 3: If the above didn't work, try the Next button
                    survey_still_visible = page.get_by_text("How likely are you to recommend us to a friend or colleague?").is_visible(timeout=timeouts['button_wait'])
                    if survey_still_visible:
                        next_button = page.get_by_role("button", name="Next")
                        if next_button.is_visible(timeout=timeouts['button_wait']):
                            print("Clicking Next button on NPS survey")
                            next_button.click()
                            page.wait_for_timeout(timeouts['action_wait'])

                            # After clicking Next, try to click Skip
                            skip_button = page.get_by_role("button", name="Skip")
                            if skip_button.is_visible(timeout=timeouts['button_wait']):
                                print("Clicking Skip button after Next")
                                skip_button.click()
                                page.wait_for_timeout(timeouts['action_wait'])
        else:
            print("NPS survey popup not detected")

        # Wait a bit after handling the popup
        page.wait_for_timeout(timeouts['action_wait'])

        # Check if the popup is still visible and try one more approach if needed
        survey_still_visible = page.get_by_text("How likely are you to recommend us to a friend or colleague?").is_visible(timeout=timeouts['button_wait'])
        if survey_still_visible:
            print("Survey still visible, trying to locate and click the arrow directly")

            # Try to find the arrow by its CSS properties - it's likely a button with an icon
            # Look for elements that might be the minimize button
            minimize_button = page.locator(".nps-dismiss-button, .nps-minimize-button, .survey-minimize, .survey-close, button.minimize, button.close")
            if minimize_button.is_visible(timeout=timeouts['button_wait']):
                print("Found minimize button by CSS selector")
                minimize_button.click()
                page.wait_for_timeout(timeouts['action_wait'])
            else:
                # Try to find the triangle/arrow icon specifically
                # Based on the screenshot, there appears to be a small triangle/arrow at the top of the popup
                triangle_selectors = [
                    ".triangle, .arrow-up, .arrow-down, .caret-up, .caret-down",
                    "div[class*='triangle'], div[class*='arrow'], div[class*='caret']",
                    "span[class*='triangle'], span[class*='arrow'], span[class*='caret']",
                    "i[class*='triangle'], i[class*='arrow'], i[class*='caret']"
                ]

                found_triangle = False
                for selector in triangle_selectors:
                    triangle = page.locator(selector)
                    if triangle.is_visible(timeout=timeouts['popup_disappear']):
                        print(f"Found triangle/arrow with selector: {selector}")
                        triangle.click()
                        page.wait_for_timeout(timeouts['action_wait'])
                        found_triangle = True
                        break

                if not found_triangle:
                    # If we still can't find it, try clicking on the NINJIO logo in the popup
                    # This is visible in the screenshot at the bottom of the popup
                    ninjio_logo = page.locator("img[alt='NINJIO'], .ninjio-logo")
                    if ninjio_logo.is_visible(timeout=timeouts['button_wait']):
                        # Click slightly above the logo where the arrow might be
                        box = ninjio_logo.bounding_box()
                        if box:
                            page.mouse.click(box["x"] + box["width"] / 2, box["y"] - 20)
                            print("Clicked above the NINJIO logo")
                            page.wait_for_timeout(timeouts['action_wait'])
                    else:
                        # Last resort: try clicking at specific coordinates where the arrow might be
                        # Based on the screenshot, the arrow appears to be at the top of the popup
                        print("Trying to click at specific coordinates where the arrow might be")
                        # Click in the top-center of the visible viewport
                        viewport_size = page.viewport_size
                        if viewport_size:
                            # Click near the bottom-right where the popup is, then at the top of that area
                            page.mouse.click(viewport_size["width"] - 100, viewport_size["height"] - 50)
                            page.wait_for_timeout(timeouts['popup_disappear'])
                            page.mouse.click(viewport_size["width"] - 100, viewport_size["height"] - 150)
                            page.wait_for_timeout(timeouts['action_wait'])
    except Exception as e:
        print(f"Error handling NPS survey popup: {str(e)}")
        # Continue with the test even if there's an error handling the popup
        pass


def dismiss_popups(page: Page) -> None:
    """
    Helper function to dismiss any popups or tutorials that might appear.
    Uses environment-aware timeouts for better reliability.
    """
    # Get appropriate timeouts for current browser mode
    timeouts = get_timeouts(page)

    # Try to dismiss popups, but don't fail the test if they're not present
    try:
        # Skip tutorial popups if they appear
        for _ in range(3):
            skip_button = page.get_by_role("button", name="Skip")
            if skip_button.is_visible(timeout=timeouts['button_wait']):
                skip_button.click()
                page.wait_for_timeout(timeouts['action_wait'])
            else:
                break

        # Close any other dialogs
        close_button = page.get_by_role("button", name="Close")
        if close_button.is_visible(timeout=timeouts['button_wait']):
            close_button.click()
            page.wait_for_timeout(timeouts['action_wait'])
    except:
        # Continue with the test if popups are not present
        pass
