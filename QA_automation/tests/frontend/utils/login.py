"""
Utility module for handling login functionality in tests.
"""
import re
import os
from playwright.sync_api import Page, expect
from popup_handlers import handle_survey_popup, dismiss_popups

# Environment variables for login credentials (no fallback values - explicit configuration required)
PROD_LOGIN_USERNAME = os.getenv("PROD_LOGIN_USERNAME")
PROD_LOGIN_PASSWORD = os.getenv("PROD_LOGIN_PASSWORD")
STAGING_LOGIN_USERNAME = os.getenv("STAGING_LOGIN_USERNAME")
STAGING_LOGIN_PASSWORD = os.getenv("STAGING_LOGIN_PASSWORD")


def validate_login_credentials(environment: str, username: str = None, password: str = None):
    """
    Validate that login credentials are available either from parameters or environment variables.

    Args:
        environment: The environment ("production" or "staging")
        username: Username parameter (optional)
        password: Password parameter (optional)

    Returns:
        tuple: (validated_username, validated_password)

    Raises:
        ValueError: If credentials are not available
    """
    if environment.lower() == "production":
        env_username = PROD_LOGIN_USERNAME
        env_password = PROD_LOGIN_PASSWORD
        env_var_names = ["PROD_LOGIN_USERNAME", "PROD_LOGIN_PASSWORD"]
    elif environment.lower() == "staging":
        env_username = STAGING_LOGIN_USERNAME
        env_password = STAGING_LOGIN_PASSWORD
        env_var_names = ["STAGING_LOGIN_USERNAME", "STAGING_LOGIN_PASSWORD"]
    else:
        raise ValueError(f"Unsupported environment: {environment}")

    # Use provided credentials if available, otherwise use environment variables
    final_username = username if username is not None else env_username
    final_password = password if password is not None else env_password

    # Check if credentials are available
    missing_vars = []
    if final_username is None:
        missing_vars.append(env_var_names[0])
    if final_password is None:
        missing_vars.append(env_var_names[1])

    if missing_vars:
        raise ValueError(
            f"Missing required login credentials for {environment} environment. "
            f"Please set environment variables: {', '.join(missing_vars)} "
            f"or provide credentials as function parameters."
        )

    return final_username, final_password


def login_to_production(page: Page, username: str = None, password: str = None) -> Page:
    """
    Login to the production environment.

    Args:
        page: The Playwright Page object
        username: Username for login (default: from PROD_LOGIN_USERNAME env var)
        password: Password for login (default: from PROD_LOGIN_PASSWORD env var)

    Returns:
        The Page object after successful login

    Raises:
        ValueError: If credentials are not available from environment variables or parameters
    """
    # Validate and get credentials
    username, password = validate_login_credentials("production", username, password)
    # Navigate to the production login page
    page.goto('https://admin.goninjio.com/login')

    # Fill in the login form
    page.get_by_role('textbox', name='Username').click()
    page.get_by_role('textbox', name='Username').fill(username)
    page.get_by_role('textbox', name='Password').click()
    page.get_by_role('textbox', name='Password').fill(password)

    # Click the login button
    page.get_by_role('button', name='Login').click()

    # Wait for navigation to complete after successful login
    page.wait_for_url("**/react/**", timeout=30000)

    # Verify we're logged in by checking the URL
    expect(page).to_have_url(re.compile(r".*/react/.*"))

    # Make sure we're on a known page
    if not page.url.startswith('https://admin.goninjio.com/react/'):
        page.goto('https://admin.goninjio.com/react/aware')

    # Handle the NPS survey popup that appears after login
    handle_survey_popup(page)

    # Handle any other popups that might appear
    dismiss_popups(page)

    print("Login to production successful!")
    return page


def login_to_staging(page: Page, username: str = None, password: str = None) -> Page:
    """
    Login to the staging environment.

    Args:
        page: The Playwright Page object
        username: Username for login (default: from STAGING_LOGIN_USERNAME env var)
        password: Password for login (default: from STAGING_LOGIN_PASSWORD env var)

    Returns:
        The Page object after successful login

    Raises:
        ValueError: If credentials are not available from environment variables or parameters
    """
    # Validate and get credentials
    username, password = validate_login_credentials("staging", username, password)
    # Navigate to the staging login page
    page.goto('https://dashboard.cyber-pal.online/login/')

    # Fill in the login form
    page.get_by_role('textbox', name='Username').click()
    page.get_by_role('textbox', name='Username').fill(username)
    page.get_by_role('textbox', name='Password').click()
    page.get_by_role('textbox', name='Password').fill(password)

    # Click the login button
    page.get_by_role('button', name='Login').click()

    # Wait for navigation to complete after successful login
    page.wait_for_url("**/react/**", timeout=30000)

    # Verify we're logged in by checking the URL
    expect(page).to_have_url(re.compile(r".*/react/.*"))

    # Make sure we're on a known page
    if not page.url.startswith('https://dashboard.cyber-pal.online/react/'):
        page.goto('https://dashboard.cyber-pal.online/react/aware')

    # Handle the NPS survey popup that appears after login
    handle_survey_popup(page)

    # Handle any other popups that might appear
    dismiss_popups(page)

    print("Login to staging successful!")
    return page


def login(page: Page, environment: str = "production", username: str = None, password: str = None) -> Page:
    """
    Generic login function that can be used for different environments.

    Args:
        page: The Playwright Page object
        environment: The environment to login to ("production" or "staging")
        username: Username for login (default: from environment variables)
        password: Password for login (default: from environment variables)

    Returns:
        The Page object after successful login

    Raises:
        ValueError: If credentials are not available from environment variables or parameters
    """
    # Validate and get credentials
    username, password = validate_login_credentials(environment, username, password)
    if environment.lower() == "production":
        return login_to_production(page, username, password)
    elif environment.lower() == "staging":
        return login_to_staging(page, username, password)
    else:
        raise ValueError(f"Unknown environment: {environment}. Use 'production' or 'staging'.")
