# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTest
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Node.js (for @playwright/test)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test artifacts
screenshots/
videos/
traces/
downloads/
auth-state.json
*.png
*.jpg
*.jpeg
*.gif

# Logs
*.log
logs/

# Documentation files (README, test results, etc.)
*.md

# Backup folder - don't upload to production branch
tests/backup/
