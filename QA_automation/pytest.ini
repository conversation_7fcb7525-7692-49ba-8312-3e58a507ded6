[pytest]
testpaths = tests
pythonpath = .
# Isolate this project from parent configurations
norecursedirs = ../* ../tests ../venv ../node_modules
collect_ignore = ../tests
confcutdir = .
markers =
    main: main test bundle.
    todo: Mark unimplemented test.
    dal: data access layer tests.
    integration: tests that required integration
    stress: Mark for stress tests
    frontend: frontend tests
    api: api tests
    tenants: tenant tests
    prod: production tests
    smoke: smoke tests
    regression: regression tests
    production: Production environment tests
    user-creation: User creation tests
    user-edit: User editing tests
    user-deletion: User deletion tests
    login: Login tests
    email: Email template tests
    api-key: API key tests
    smart_training: Smart training sequence tests
    totp: TOTP authentication tests
asyncio_mode = auto
addopts = -p no:warnings -p no:django --tb=short --strict-markers --rootdir=.
plugins = pytest_playwright
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
minversion = 6.0
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S