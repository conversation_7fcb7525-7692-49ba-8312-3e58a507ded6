import logging
import sys


def _is_debug():
    """
    Check if the code is running in debug mode
    :return: True if the code is running in debug mode, False otherwise

    credit: https://stackoverflow.com/a/71170397/11325201
    """
    gettrace = getattr(sys, 'gettrace', None)
    if gettrace is None:
        return False
    else:
        v = gettrace()
        if v is None:
            return False
        else:
            return True


class QALogger:
    def __init__(self, name='test_logger'):
        self.logger = logging.getLogger(name)

        if not _is_debug():
            self.logger.setLevel(logging.INFO)
        else:
            self.logger.setLevel(logging.DEBUG)

        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def get_logger(self):
        return self.logger
