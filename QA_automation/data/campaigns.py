from datetime import datetime, timedelta


class Campaigns:
    current_time = datetime.now().strftime("%B %d, %Y at %I:%M %p")
    CAMPAIGN_NAME_SENSE = f"test_QA automation campaign created on"
    # CAMPAIGN_NAME_PHISHING = f"Phishing test_QA automation campaign created on {current_time}"
    CAMPAIGN_NAME_PHISHING = f"Phishing test_QA automation campaign created on"

    CLOSING_CAMPAIGN_CONFIG = (
        '{"data":{"test_QA":[]},'
        '"charts":{},'
        '"filters":{"departments":["N/A","R and D","הנדסה/תיכנון ביצוע","fdsfsdfsdfsdfsdfsdf","a"],'
        '"branches":["zizi","SW","שיכון ובינוי נדל\\"ן בע\\"מ","א\\".ס\\" הנדסה","(blank,)","N/A",'
        '"sdfsdfsdf@@@FDSFSDFSDFSDFDSSCXZCZXCZX5435435435423432","b"],'
        '"groups":[],"luredEvents":[]}}'
    )
